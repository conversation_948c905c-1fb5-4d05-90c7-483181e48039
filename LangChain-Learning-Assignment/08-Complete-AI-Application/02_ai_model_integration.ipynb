# Model characteristics database
import pandas as pd
from dataclasses import dataclass
from typing import Dict, List

@dataclass
class ModelInfo:
    """Information about an AI model"""
    name: str
    provider: str
    speed: str  # "fast", "medium", "slow"
    cost: str   # "low", "medium", "high"
    quality: str # "good", "very good", "excellent"
    best_for: List[str]
    context_length: int  # Maximum tokens
    
# Define our available models
AVAILABLE_MODELS = {
    "gpt-3.5-turbo": ModelInfo(
        name="gpt-3.5-turbo",
        provider="OpenAI",
        speed="medium",
        cost="medium",
        quality="very good",
        best_for=["general chat", "simple analysis", "quick responses"],
        context_length=4096
    ),
    "gpt-4": ModelInfo(
        name="gpt-4",
        provider="OpenAI",
        speed="slow",
        cost="high",
        quality="excellent",
        best_for=["complex reasoning", "creative writing", "detailed analysis"],
        context_length=8192
    ),
    "gemma2-9b-it": ModelInfo(
        name="gemma2-9b-it",
        provider="Groq",
        speed="fast",
        cost="low",
        quality="good",
        best_for=["quick responses", "simple tasks", "high volume"],
        context_length=8192
    ),
    "llama3-70b-8192": ModelInfo(
        name="llama3-70b-8192",
        provider="Groq",
        speed="fast",
        cost="low",
        quality="very good",
        best_for=["code generation", "technical tasks", "reasoning"],
        context_length=8192
    )
}

# Display model comparison
print("🤖 Available AI Models Comparison")
print("=" * 60)

# Create comparison table
model_data = []
for model_id, info in AVAILABLE_MODELS.items():
    model_data.append({
        "Model": info.name,
        "Provider": info.provider,
        "Speed": info.speed,
        "Cost": info.cost,
        "Quality": info.quality,
        "Context Length": info.context_length,
        "Best For": ", ".join(info.best_for[:2])  # Show first 2 use cases
    })

df = pd.DataFrame(model_data)
print(df.to_string(index=False))

print("\n💡 Model Selection Guidelines:")
print("  🚀 Fast + Cheap: Use Groq models for simple tasks")
print("  ⚖️ Balanced: Use GPT-3.5-turbo for general purposes")
print("  🧠 Best Quality: Use GPT-4 for complex reasoning")

# Professional AI Model Manager
import os
import time
from typing import Optional, Dict, Any
from dotenv import load_dotenv
from langchain_openai import ChatOpenAI
from langchain_groq import ChatGroq

class AIModelManager:
    """
    Professional AI Model Manager
    
    This class manages multiple AI models and provides:
    - Easy model switching
    - Automatic fallbacks
    - Performance monitoring
    - Error handling
    """
    
    def __init__(self):
        """Initialize the model manager"""
        # Load environment variables
        load_dotenv()
        
        # Set up environment variables
        self._setup_environment()
        
        # Initialize model storage
        self.models: Dict[str, Any] = {}
        self.current_model: Optional[str] = None
        self.performance_stats: Dict[str, Dict] = {}
        
        # Initialize available models
        self._initialize_models()
        
        print("✅ AI Model Manager initialized")
        print(f"📊 Available models: {list(self.models.keys())}")
    
    def _setup_environment(self):
        """Set up environment variables for AI services"""
        os.environ["OPENAI_API_KEY"] = os.getenv("OPENAI_API_KEY", "")
        os.environ["GROQ_API_KEY"] = os.getenv("GROQ_API_KEY", "")
        os.environ["LANGCHAIN_API_KEY"] = os.getenv("LANGCHAIN_API_KEY", "")
        os.environ["LANGCHAIN_PROJECT"] = os.getenv("LANGCHAIN_PROJECT", "AI-Application")
        os.environ["LANGCHAIN_TRACING_V2"] = "true"
    
    def _initialize_models(self):
        """Initialize all available AI models"""
        print("🔧 Initializing AI models...")
        
        # OpenAI Models
        try:
            self.models["gpt-3.5-turbo"] = ChatOpenAI(
                model="gpt-3.5-turbo",
                temperature=0.7,
                max_tokens=1000
            )
            print("  ✅ GPT-3.5-turbo initialized")
        except Exception as e:
            print(f"  ❌ GPT-3.5-turbo failed: {e}")
        
        try:
            self.models["gpt-4"] = ChatOpenAI(
                model="gpt-4",
                temperature=0.7,
                max_tokens=1000
            )
            print("  ✅ GPT-4 initialized")
        except Exception as e:
            print(f"  ❌ GPT-4 failed: {e}")
        
        # Groq Models
        try:
            self.models["gemma2-9b-it"] = ChatGroq(
                model="gemma2-9b-it",
                temperature=0.7,
                max_tokens=1000
            )
            print("  ✅ Gemma2-9B initialized")
        except Exception as e:
            print(f"  ❌ Gemma2-9B failed: {e}")
        
        try:
            self.models["llama3-70b-8192"] = ChatGroq(
                model="llama3-70b-8192",
                temperature=0.7,
                max_tokens=1000
            )
            print("  ✅ Llama3-70B initialized")
        except Exception as e:
            print(f"  ❌ Llama3-70B failed: {e}")
        
        # Set default model
        if self.models:
            self.current_model = list(self.models.keys())[0]
            print(f"🎯 Default model set to: {self.current_model}")
        else:
            print("❌ No models available! Check your API keys.")

# Create and test the model manager
print("🚀 Creating AI Model Manager...")
model_manager = AIModelManager()

# Add model selection methods to our manager
class AIModelManager(AIModelManager):  # Extend our existing class
    
    def get_available_models(self) -> List[str]:
        """Get list of available model names"""
        return list(self.models.keys())
    
    def set_model(self, model_name: str) -> bool:
        """Set the current active model"""
        if model_name in self.models:
            self.current_model = model_name
            print(f"✅ Switched to model: {model_name}")
            return True
        else:
            print(f"❌ Model '{model_name}' not available")
            print(f"Available models: {self.get_available_models()}")
            return False
    
    def get_current_model(self) -> Optional[str]:
        """Get the currently active model name"""
        return self.current_model
    
    def recommend_model(self, task_type: str, priority: str = "balanced") -> str:
        """
        Recommend the best model for a specific task
        
        Args:
            task_type: Type of task ("simple", "complex", "creative", "technical")
            priority: What to optimize for ("speed", "quality", "cost", "balanced")
        
        Returns:
            Recommended model name
        """
        recommendations = {
            "simple": {
                "speed": "gemma2-9b-it",
                "quality": "gpt-3.5-turbo",
                "cost": "gemma2-9b-it",
                "balanced": "gemma2-9b-it"
            },
            "complex": {
                "speed": "llama3-70b-8192",
                "quality": "gpt-4",
                "cost": "llama3-70b-8192",
                "balanced": "gpt-3.5-turbo"
            },
            "creative": {
                "speed": "llama3-70b-8192",
                "quality": "gpt-4",
                "cost": "llama3-70b-8192",
                "balanced": "gpt-3.5-turbo"
            },
            "technical": {
                "speed": "llama3-70b-8192",
                "quality": "gpt-4",
                "cost": "llama3-70b-8192",
                "balanced": "llama3-70b-8192"
            }
        }
        
        recommended = recommendations.get(task_type, {}).get(priority, "gpt-3.5-turbo")
        
        # Check if recommended model is available
        if recommended in self.models:
            return recommended
        else:
            # Fallback to first available model
            return self.get_available_models()[0] if self.models else None
    
    def auto_select_model(self, task_type: str, priority: str = "balanced") -> bool:
        """Automatically select and switch to the best model for a task"""
        recommended = self.recommend_model(task_type, priority)
        if recommended:
            return self.set_model(recommended)
        return False

# Test model selection
print("🧪 Testing Model Selection...")
print("=" * 40)

# Test different scenarios
scenarios = [
    ("simple", "speed", "Quick FAQ response"),
    ("complex", "quality", "Detailed analysis"),
    ("creative", "balanced", "Story writing"),
    ("technical", "speed", "Code explanation")
]

for task_type, priority, description in scenarios:
    recommended = model_manager.recommend_model(task_type, priority)
    print(f"📋 Task: {description}")
    print(f"   Type: {task_type}, Priority: {priority}")
    print(f"   Recommended: {recommended}")
    print()

# Add performance monitoring to our model manager
import time
from datetime import datetime

class AIModelManager(AIModelManager):  # Extend our existing class
    
    def invoke_with_monitoring(self, message: str, model_name: Optional[str] = None) -> Dict[str, Any]:
        """
        Invoke a model with performance monitoring
        
        Args:
            message: The message to send to the AI
            model_name: Specific model to use (optional)
        
        Returns:
            Dictionary with response and performance metrics
        """
        # Use specified model or current model
        model_to_use = model_name or self.current_model
        
        if not model_to_use or model_to_use not in self.models:
            return {
                "success": False,
                "error": f"Model '{model_to_use}' not available",
                "response": None,
                "metrics": {}
            }
        
        # Initialize performance stats for this model if needed
        if model_to_use not in self.performance_stats:
            self.performance_stats[model_to_use] = {
                "total_requests": 0,
                "successful_requests": 0,
                "total_response_time": 0,
                "average_response_time": 0,
                "last_used": None
            }
        
        # Start timing
        start_time = time.time()
        
        try:
            # Get the model and invoke it
            model = self.models[model_to_use]
            response = model.invoke(message)
            
            # Calculate response time
            response_time = time.time() - start_time
            
            # Update performance stats
            stats = self.performance_stats[model_to_use]
            stats["total_requests"] += 1
            stats["successful_requests"] += 1
            stats["total_response_time"] += response_time
            stats["average_response_time"] = stats["total_response_time"] / stats["total_requests"]
            stats["last_used"] = datetime.now().isoformat()
            
            return {
                "success": True,
                "response": response.content,
                "model_used": model_to_use,
                "metrics": {
                    "response_time": response_time,
                    "response_length": len(response.content),
                    "timestamp": datetime.now().isoformat()
                }
            }
            
        except Exception as e:
            # Update failure stats
            response_time = time.time() - start_time
            stats = self.performance_stats[model_to_use]
            stats["total_requests"] += 1
            stats["total_response_time"] += response_time
            stats["average_response_time"] = stats["total_response_time"] / stats["total_requests"]
            
            return {
                "success": False,
                "error": str(e),
                "response": None,
                "model_used": model_to_use,
                "metrics": {
                    "response_time": response_time,
                    "timestamp": datetime.now().isoformat()
                }
            }
    
    def get_performance_report(self) -> str:
        """Generate a performance report for all models"""
        if not self.performance_stats:
            return "No performance data available yet."
        
        report = "📊 Model Performance Report\n"
        report += "=" * 50 + "\n\n"
        
        for model_name, stats in self.performance_stats.items():
            success_rate = (stats["successful_requests"] / stats["total_requests"] * 100) if stats["total_requests"] > 0 else 0
            
            report += f"🤖 {model_name}:\n"
            report += f"   Total Requests: {stats['total_requests']}\n"
            report += f"   Success Rate: {success_rate:.1f}%\n"
            report += f"   Avg Response Time: {stats['average_response_time']:.2f}s\n"
            report += f"   Last Used: {stats['last_used'] or 'Never'}\n\n"
        
        return report

# Test performance monitoring
print("🧪 Testing Performance Monitoring...")
print("=" * 40)

# Test with different models
test_message = "What is artificial intelligence in one sentence?"

for model_name in model_manager.get_available_models()[:2]:  # Test first 2 models
    print(f"\n🤖 Testing {model_name}...")
    result = model_manager.invoke_with_monitoring(test_message, model_name)
    
    if result["success"]:
        print(f"✅ Success in {result['metrics']['response_time']:.2f}s")
        print(f"📝 Response: {result['response'][:100]}...")
    else:
        print(f"❌ Failed: {result['error']}")

# Show performance report
print("\n" + model_manager.get_performance_report())

# Model comparison and benchmarking
def compare_models(model_manager: AIModelManager, test_prompts: List[str], models_to_test: Optional[List[str]] = None) -> Dict:
    """
    Compare multiple models on the same set of prompts
    
    Args:
        model_manager: The AI model manager instance
        test_prompts: List of prompts to test
        models_to_test: Specific models to test (optional)
    
    Returns:
        Comparison results
    """
    if models_to_test is None:
        models_to_test = model_manager.get_available_models()
    
    results = {
        "prompts": test_prompts,
        "models": models_to_test,
        "responses": {},
        "metrics": {}
    }
    
    print(f"🔬 Comparing {len(models_to_test)} models on {len(test_prompts)} prompts...")
    print("=" * 60)
    
    for i, prompt in enumerate(test_prompts):
        print(f"\n📝 Prompt {i+1}: {prompt[:50]}...")
        results["responses"][i] = {}
        results["metrics"][i] = {}
        
        for model_name in models_to_test:
            print(f"  🤖 Testing {model_name}...")
            
            result = model_manager.invoke_with_monitoring(prompt, model_name)
            
            if result["success"]:
                results["responses"][i][model_name] = result["response"]
                results["metrics"][i][model_name] = result["metrics"]
                print(f"    ✅ {result['metrics']['response_time']:.2f}s")
            else:
                results["responses"][i][model_name] = f"ERROR: {result['error']}"
                results["metrics"][i][model_name] = {"error": result["error"]}
                print(f"    ❌ Failed")
    
    return results

# Define test prompts for comparison
test_prompts = [
    "What is machine learning? Explain in one paragraph.",
    "Write a haiku about programming.",
    "Explain the difference between AI and ML.",
    "What are the benefits of using Python for data science?"
]

# Run comparison (limit to 2 models to save time)
available_models = model_manager.get_available_models()
models_to_compare = available_models[:2] if len(available_models) >= 2 else available_models

if models_to_compare:
    comparison_results = compare_models(model_manager, test_prompts[:2], models_to_compare)  # Test 2 prompts
    
    # Display comparison summary
    print("\n📊 Comparison Summary:")
    print("=" * 40)
    
    for i, prompt in enumerate(comparison_results["prompts"]):
        print(f"\n📝 Prompt: {prompt[:40]}...")
        
        for model_name in comparison_results["models"]:
            response = comparison_results["responses"][i].get(model_name, "No response")
            metrics = comparison_results["metrics"][i].get(model_name, {})
            
            if "error" not in metrics:
                response_time = metrics.get("response_time", 0)
                response_length = metrics.get("response_length", 0)
                print(f"  🤖 {model_name}: {response_time:.2f}s, {response_length} chars")
                print(f"     {response[:80]}...")
            else:
                print(f"  🤖 {model_name}: ERROR")
else:
    print("❌ No models available for comparison")