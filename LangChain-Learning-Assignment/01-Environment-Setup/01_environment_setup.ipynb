{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 📚 Assignment 01: Environment Setup\n", "\n", "## 🎯 What We're Going to Do\n", "In this notebook, we'll verify that our environment is set up correctly and test our connections to AI services.\n", "\n", "## 🧠 Learning Goals\n", "- Understand how to load environment variables\n", "- Test package installations\n", "- Verify API key setup\n", "- Make our first connection to an AI model"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 1: Test Package Imports\n", "\n", "**What we're doing**: Checking if all required packages are installed correctly\n", "\n", "**Why this matters**: If packages aren't installed, nothing else will work\n", "\n", "**What to expect**: If successful, you'll see \"✅ All packages imported successfully!\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Test 1: Import all required packages\n", "try:\n", "    import os\n", "    from dotenv import load_dotenv\n", "    import langchain\n", "    from langchain_openai import ChatOpenAI\n", "    from langchain_groq import ChatGroq\n", "    from langchain_core.prompts import ChatPromptTemplate\n", "    from langchain_core.output_parsers import StrOutputParser\n", "    \n", "    print(\"✅ All packages imported successfully!\")\n", "    print(f\"📦 LangChain version: {langchain.__version__}\")\n", "    \n", "except ImportError as e:\n", "    print(f\"❌ Import error: {e}\")\n", "    print(\"💡 Solution: Make sure you installed all packages with pip install\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 2: Load Environment Variables\n", "\n", "**What we're doing**: Loading our API keys from the .env file\n", "\n", "**Why this matters**: Without API keys, we can't connect to AI services\n", "\n", "**What to expect**: You should see \"True\" if the .env file is found and loaded"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Test 2: Load environment variables\n", "result = load_dotenv()\n", "print(f\"Environment file loaded: {result}\")\n", "\n", "if result:\n", "    print(\"✅ Environment variables loaded successfully!\")\n", "else:\n", "    print(\"❌ Could not load .env file\")\n", "    print(\"💡 Solution: Make sure .env file exists in this directory\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 3: Verify API Keys\n", "\n", "**What we're doing**: Checking if our API keys are loaded correctly\n", "\n", "**Why this matters**: We need these keys to authenticate with AI services\n", "\n", "**Security note**: We only show the first few characters for security"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Test 3: Check API keys\n", "openai_key = os.getenv(\"OPENAI_API_KEY\")\n", "groq_key = os.getenv(\"GROQ_API_KEY\")\n", "langchain_key = os.getenv(\"LANGCHAIN_API_KEY\")\n", "project_name = os.getenv(\"LANGCHAIN_PROJECT\")\n", "\n", "print(\"🔑 API Key Status:\")\n", "print(f\"OpenAI Key: {'✅ Found' if openai_key else '❌ Missing'} {openai_key[:10] + '...' if openai_key else ''}\")\n", "print(f\"Groq Key: {'✅ Found' if groq_key else '❌ Missing'} {groq_key[:10] + '...' if groq_key else ''}\")\n", "print(f\"LangChain Key: {'✅ Found' if langchain_key else '❌ Missing'} {langchain_key[:10] + '...' if langchain_key else ''}\")\n", "print(f\"Project Name: {project_name}\")\n", "\n", "if all([openai_key, groq_key, langchain_key]):\n", "    print(\"\\n🎉 All API keys are configured!\")\n", "else:\n", "    print(\"\\n⚠️ Some API keys are missing. Check your .env file.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 4: Set Environment Variables for Session\n", "\n", "**What we're doing**: Making API keys available to our Python session\n", "\n", "**Why this matters**: Some packages need environment variables to be set in the session\n", "\n", "**What happens**: We copy keys from .env file to session environment"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Test 4: Set environment variables for this session\n", "os.environ[\"OPENAI_API_KEY\"] = os.getenv(\"OPENAI_API_KEY\")\n", "os.environ[\"GROQ_API_KEY\"] = os.getenv(\"GROQ_API_KEY\")\n", "os.environ[\"LANGCHAIN_API_KEY\"] = os.getenv(\"LANGCHAIN_API_KEY\")\n", "os.environ[\"LANGCHAIN_PROJECT\"] = os.getenv(\"LANGCHAIN_PROJECT\")\n", "os.environ[\"LANGCHAIN_TRACING_V2\"] = \"true\"\n", "\n", "print(\"✅ Environment variables set for this session!\")\n", "print(\"📊 LangChain tracing enabled - you can monitor your AI calls\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 5: Test OpenAI Connection\n", "\n", "**What we're doing**: Creating a connection to OpenAI's GPT model\n", "\n", "**Why this matters**: This tests if our OpenAI API key works\n", "\n", "**What to expect**: You should see model information if successful"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Test 5: Test OpenAI connection\n", "try:\n", "    openai_model = ChatOpenAI(model=\"gpt-3.5-turbo\")\n", "    print(\"✅ OpenAI model created successfully!\")\n", "    print(f\"📱 Model: {openai_model.model_name}\")\n", "    print(f\"🌡️ Temperature: {openai_model.temperature}\")\n", "    \n", "except Exception as e:\n", "    print(f\"❌ OpenAI connection failed: {e}\")\n", "    print(\"💡 Check your OPENAI_API_KEY in the .env file\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 6: <PERSON> Groq Connection\n", "\n", "**What we're doing**: Creating a connection to Groq's AI model\n", "\n", "**Why this matters**: This tests if our Groq API key works\n", "\n", "**What to expect**: You should see model information if successful"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Test 6: Test Groq connection\n", "try:\n", "    groq_model = ChatGroq(model=\"gemma2-9b-it\")\n", "    print(\"✅ Groq model created successfully!\")\n", "    print(f\"📱 Model: {groq_model.model_name}\")\n", "    \n", "except Exception as e:\n", "    print(f\"❌ Groq connection failed: {e}\")\n", "    print(\"💡 Check your GROQ_API_KEY in the .env file\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 7: Test AI Response\n", "\n", "**What we're doing**: Asking the AI a simple question to test everything works\n", "\n", "**Why this matters**: This is the ultimate test - can we actually get AI responses?\n", "\n", "**What to expect**: You should get a response about AI"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Test 7: Test actual AI response\n", "try:\n", "    # Use Groq for faster response\n", "    test_model = ChatGroq(model=\"gemma2-9b-it\")\n", "    response = test_model.invoke(\"What is AI in one sentence?\")\n", "    \n", "    print(\"✅ AI response test successful!\")\n", "    print(f\"🤖 AI Response: {response.content}\")\n", "    print(f\"📊 Response length: {len(response.content)} characters\")\n", "    \n", "except Exception as e:\n", "    print(f\"❌ AI response test failed: {e}\")\n", "    print(\"💡 Check your internet connection and API keys\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🎯 Assignment Complete!\n", "\n", "If all the tests above passed, congratulations! You have successfully:\n", "\n", "✅ Set up your conda environment  \n", "✅ Installed all required packages  \n", "✅ Configured your API keys  \n", "✅ Connected to AI models  \n", "✅ Got your first AI response  \n", "\n", "## 🚀 What's Next?\n", "\n", "Now you're ready to move to **Assignment 02: Basic AI Connection** where you'll learn:\n", "- How different AI models work\n", "- How to choose the right model for your task\n", "- How to handle AI responses\n", "\n", "## 🎓 What You Learned\n", "\n", "In this assignment, you learned:\n", "1. **Environment management** - How to create isolated workspaces\n", "2. **Package installation** - How to install Python packages\n", "3. **Security practices** - How to handle API keys safely\n", "4. **Testing** - How to verify your setup works\n", "5. **AI connections** - How to connect to AI services\n", "\n", "Great job! 🎉"]}], "metadata": {"kernelspec": {"display_name": "langchain_learning", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.0"}}, "nbformat": 4, "nbformat_minor": 2}