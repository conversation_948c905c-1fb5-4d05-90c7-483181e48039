# Test 1: Import all required packages
try:
    import os
    from dotenv import load_dotenv
    import langchain
    from langchain_openai import ChatOpenAI
    from langchain_groq import ChatGroq
    from langchain_core.prompts import ChatPromptTemplate
    from langchain_core.output_parsers import StrOutputParser
    
    print("✅ All packages imported successfully!")
    print(f"📦 LangChain version: {langchain.__version__}")
    
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("💡 Solution: Make sure you installed all packages with pip install")

# Test 2: Load environment variables
result = load_dotenv()
print(f"Environment file loaded: {result}")

if result:
    print("✅ Environment variables loaded successfully!")
else:
    print("❌ Could not load .env file")
    print("💡 Solution: Make sure .env file exists in this directory")

# Test 3: Check API keys
openai_key = os.getenv("OPENAI_API_KEY")
groq_key = os.getenv("GROQ_API_KEY")
langchain_key = os.getenv("LANGCHAIN_API_KEY")
project_name = os.getenv("LANGCHAIN_PROJECT")

print("🔑 API Key Status:")
print(f"OpenAI Key: {'✅ Found' if openai_key else '❌ Missing'} {openai_key[:10] + '...' if openai_key else ''}")
print(f"Groq Key: {'✅ Found' if groq_key else '❌ Missing'} {groq_key[:10] + '...' if groq_key else ''}")
print(f"LangChain Key: {'✅ Found' if langchain_key else '❌ Missing'} {langchain_key[:10] + '...' if langchain_key else ''}")
print(f"Project Name: {project_name}")

if all([openai_key, groq_key, langchain_key]):
    print("\n🎉 All API keys are configured!")
else:
    print("\n⚠️ Some API keys are missing. Check your .env file.")

# Test 4: Set environment variables for this session
os.environ["OPENAI_API_KEY"] = os.getenv("OPENAI_API_KEY")
os.environ["GROQ_API_KEY"] = os.getenv("GROQ_API_KEY")
os.environ["LANGCHAIN_API_KEY"] = os.getenv("LANGCHAIN_API_KEY")
os.environ["LANGCHAIN_PROJECT"] = os.getenv("LANGCHAIN_PROJECT")
os.environ["LANGCHAIN_TRACING_V2"] = "true"

print("✅ Environment variables set for this session!")
print("📊 LangChain tracing enabled - you can monitor your AI calls")

# Test 5: Test OpenAI connection
try:
    openai_model = ChatOpenAI(model="gpt-3.5-turbo")
    print("✅ OpenAI model created successfully!")
    print(f"📱 Model: {openai_model.model_name}")
    print(f"🌡️ Temperature: {openai_model.temperature}")
    
except Exception as e:
    print(f"❌ OpenAI connection failed: {e}")
    print("💡 Check your OPENAI_API_KEY in the .env file")

# Test 6: Test Groq connection
try:
    groq_model = ChatGroq(model="gemma2-9b-it")
    print("✅ Groq model created successfully!")
    print(f"📱 Model: {groq_model.model_name}")
    
except Exception as e:
    print(f"❌ Groq connection failed: {e}")
    print("💡 Check your GROQ_API_KEY in the .env file")

# Test 7: Test actual AI response
try:
    # Use Groq for faster response
    test_model = ChatGroq(model="gemma2-9b-it")
    response = test_model.invoke("What is AI in one sentence?")
    
    print("✅ AI response test successful!")
    print(f"🤖 AI Response: {response.content}")
    print(f"📊 Response length: {len(response.content)} characters")
    
except Exception as e:
    print(f"❌ AI response test failed: {e}")
    print("💡 Check your internet connection and API keys")