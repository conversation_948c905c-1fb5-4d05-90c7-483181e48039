{"cells": [{"cell_type": "code", "execution_count": 3, "id": "1661b601", "metadata": {}, "outputs": [], "source": ["from dotenv import load_dotenv"]}, {"cell_type": "code", "execution_count": 4, "id": "52932171", "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["load_dotenv()"]}, {"cell_type": "code", "execution_count": 6, "id": "a50f1721", "metadata": {}, "outputs": [], "source": ["import os\n", "os.environ['HF_TOKEN']=os.getenv(\"HF_TOKEN\")"]}, {"cell_type": "code", "execution_count": 8, "id": "4b346173", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\agenticai-2.0\\env\\lib\\site-packages\\tqdm\\auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n"]}], "source": ["from langchain_huggingface import HuggingFaceEmbeddings\n", "embeddings=HuggingFaceEmbeddings(model_name=\"all-MiniLM-L6-v2\")"]}, {"cell_type": "code", "execution_count": 9, "id": "bc60b488", "metadata": {}, "outputs": [{"data": {"text/plain": ["[-0.033388253301382065,\n", " 0.034539807587862015,\n", " 0.05947452038526535,\n", " 0.05928613990545273,\n", " -0.06353535503149033,\n", " -0.06819580495357513,\n", " 0.08823321759700775,\n", " 0.03444080427289009,\n", " -0.03278525546193123,\n", " -0.01581498049199581,\n", " 0.020981740206480026,\n", " -0.018340224400162697,\n", " -0.0398322157561779,\n", " -0.08047083765268326,\n", " -0.014469297602772713,\n", " 0.033264875411987305,\n", " 0.01425926759839058,\n", " -0.034049998968839645,\n", " -0.1429157704114914,\n", " -0.023083381354808807,\n", " -0.021380195394158363,\n", " 0.0026335804723203182,\n", " -0.047292694449424744,\n", " -0.010752723552286625,\n", " -0.0686679482460022,\n", " 0.03112492524087429,\n", " 0.07594592124223709,\n", " 0.0011282898485660553,\n", " 0.01163199357688427,\n", " -0.03603924810886383,\n", " 0.0448375940322876,\n", " 0.018390730023384094,\n", " 0.12672801315784454,\n", " -0.0013597698416560888,\n", " 0.008206703700125217,\n", " 0.06909970939159393,\n", " -0.0807635560631752,\n", " -0.05841309204697609,\n", " 0.0537545308470726,\n", " 0.026227520778775215,\n", " -0.006828587502241135,\n", " -0.05635835602879524,\n", " 0.0032929538283497095,\n", " -0.07250185310840607,\n", " 0.06960917264223099,\n", " 0.03167437016963959,\n", " -0.012384760193526745,\n", " 0.02319929003715515,\n", " 0.08131051063537598,\n", " 0.00027824557037092745,\n", " -0.12659238278865814,\n", " -0.04998632147908211,\n", " -0.035652533173561096,\n", " 0.04856070876121521,\n", " 0.09733077138662338,\n", " 0.06224416568875313,\n", " -0.03750474005937576,\n", " 0.008118332363665104,\n", " 0.027651339769363403,\n", " -0.04321657121181488,\n", " 0.016248533502221107,\n", " 0.002286770148202777,\n", " 0.0030970009975135326,\n", " -0.015322051011025906,\n", " 0.03742997348308563,\n", " -0.010506551712751389,\n", " -0.0532149076461792,\n", " -0.03969472646713257,\n", " -0.0528775230050087,\n", " -0.03044816106557846,\n", " -0.011697012931108475,\n", " 0.07245180755853653,\n", " -0.0721343457698822,\n", " 0.03910220041871071,\n", " -0.0371636226773262,\n", " 0.026422172784805298,\n", " 0.026734236627817154,\n", " -0.031146958470344543,\n", " 0.06340933591127396,\n", " -0.01694669760763645,\n", " 0.006343618035316467,\n", " -0.025483034551143646,\n", " -0.015998907387256622,\n", " 0.014684749767184258,\n", " -0.04103194549679756,\n", " 0.056689344346523285,\n", " 0.05189083144068718,\n", " 0.012766501866281033,\n", " 0.010396687313914299,\n", " 0.03603464737534523,\n", " -0.0748724564909935,\n", " 0.022285474464297295,\n", " 0.05366932228207588,\n", " 0.021017182618379593,\n", " 0.010703176259994507,\n", " 0.0112093985080719,\n", " 0.02216530404984951,\n", " -0.03350185975432396,\n", " -0.13790269196033478,\n", " 0.17661644518375397,\n", " 0.032913558185100555,\n", " 0.07648540288209915,\n", " -0.056457702070474625,\n", " 0.03820196911692619,\n", " -0.058868005871772766,\n", " 0.01618369109928608,\n", " -0.0029855151660740376,\n", " 0.008209955878555775,\n", " 0.026603639125823975,\n", " 0.03410133346915245,\n", " -0.003371243365108967,\n", " -0.04683651402592659,\n", " 0.029342370107769966,\n", " -0.018165383487939835,\n", " 0.09393955767154694,\n", " 0.01667371578514576,\n", " -0.016232196241617203,\n", " 0.07100319117307663,\n", " 0.041898343712091446,\n", " -0.012875005602836609,\n", " 0.027333280071616173,\n", " -0.037691742181777954,\n", " -0.010565751232206821,\n", " 0.08810010552406311,\n", " 0.033384472131729126,\n", " -0.0023001113440841436,\n", " -0.020081738010048866,\n", " -3.4389641919702815e-33,\n", " 0.0038853201549500227,\n", " -0.04569804668426514,\n", " 0.036723773926496506,\n", " 0.1097324788570404,\n", " 0.005294204223901033,\n", " -0.0338403545320034,\n", " -0.05718807876110077,\n", " -0.03098648600280285,\n", " 0.02820846065878868,\n", " 0.014682861045002937,\n", " -0.00561386626213789,\n", " 0.028144512325525284,\n", " -0.06591305881738663,\n", " 0.016082247719168663,\n", " 0.04844913259148598,\n", " 0.07509638369083405,\n", " -0.0042931451462209225,\n", " 0.02630463056266308,\n", " -0.02851446345448494,\n", " 0.022873923182487488,\n", " -0.052803393453359604,\n", " -0.06066505238413811,\n", " 0.01952851191163063,\n", " 0.05410182103514671,\n", " 0.02175355888903141,\n", " -0.025309912860393524,\n", " 0.031614772975444794,\n", " -0.12111620604991913,\n", " 0.04928326979279518,\n", " 0.002734114183112979,\n", " -0.03895937651395798,\n", " -0.023507410660386086,\n", " 0.01039070263504982,\n", " 0.026675866916775703,\n", " -0.020386051386594772,\n", " 0.013104562647640705,\n", " 0.012831258587539196,\n", " -0.027721118181943893,\n", " -0.07600448280572891,\n", " 0.03969907388091087,\n", " -0.06335613131523132,\n", " 0.060143597424030304,\n", " 0.0524851493537426,\n", " 0.008374635130167007,\n", " 0.015201425179839134,\n", " -0.018722601234912872,\n", " 0.012121809646487236,\n", " -0.014375045895576477,\n", " 0.03359777852892876,\n", " -0.01308041624724865,\n", " -0.06113578751683235,\n", " 0.02940639667212963,\n", " -0.09374042600393295,\n", " -0.0020575902890414,\n", " 0.024026401340961456,\n", " -0.04306023195385933,\n", " -0.03385302424430847,\n", " -0.0036304944660514593,\n", " 0.005760259460657835,\n", " 0.019234340637922287,\n", " 0.024349451065063477,\n", " 0.07109121978282928,\n", " -0.04483497887849808,\n", " 0.07316726446151733,\n", " -0.08264464139938354,\n", " 0.005428021308034658,\n", " 0.023218700662255287,\n", " 0.0609675832092762,\n", " 0.09460737556219101,\n", " -0.014135207049548626,\n", " -0.041771139949560165,\n", " -0.027401747182011604,\n", " 0.03853412717580795,\n", " -0.007350049447268248,\n", " -0.007266087923198938,\n", " 0.04803266376256943,\n", " -0.011426731944084167,\n", " -0.06901119649410248,\n", " 0.037151288241147995,\n", " -0.06476707011461258,\n", " -0.06643080711364746,\n", " -0.0011766714742407203,\n", " -0.021795984357595444,\n", " -0.007523739244788885,\n", " 0.06725428253412247,\n", " 0.008094760589301586,\n", " -0.03811290115118027,\n", " -0.08759460598230362,\n", " 0.03036234900355339,\n", " -0.04203791171312332,\n", " -0.06107678636908531,\n", " 0.012958209030330181,\n", " 0.00505085987970233,\n", " 0.01841980405151844,\n", " -0.12826380133628845,\n", " 2.6248755793083063e-33,\n", " 0.08888930827379227,\n", " 0.032136209309101105,\n", " -0.10708097368478775,\n", " -0.0162750706076622,\n", " -0.041503146290779114,\n", " 0.0020869646687060595,\n", " -0.06067206710577011,\n", " 0.12048254162073135,\n", " -0.08354717493057251,\n", " 0.050116781145334244,\n", " 0.0011806818656623363,\n", " -0.05077013745903969,\n", " 0.05996885150671005,\n", " 0.054858893156051636,\n", " 0.06972206383943558,\n", " 0.00415717251598835,\n", " 0.12240327149629593,\n", " 0.03817792981863022,\n", " -0.01899198442697525,\n", " 0.012684161774814129,\n", " -0.032571278512477875,\n", " 0.026601141318678856,\n", " -0.059803131967782974,\n", " -0.02440091408789158,\n", " -0.01654716767370701,\n", " 0.02027442306280136,\n", " -0.023540038615465164,\n", " 0.08794988691806793,\n", " -0.09462466090917587,\n", " -0.05664951354265213,\n", " 0.07793808728456497,\n", " -0.01454025786370039,\n", " -0.045985978096723557,\n", " 0.035977378487586975,\n", " 0.017227428033947945,\n", " 0.10854942351579666,\n", " 0.023995291441679,\n", " -0.09443829953670502,\n", " 0.02173480950295925,\n", " -0.035141803324222565,\n", " -0.015562971122562885,\n", " -0.009055995382368565,\n", " -0.02642057277262211,\n", " 0.0981995239853859,\n", " -0.0517328716814518,\n", " -0.05118061602115631,\n", " -0.04413079470396042,\n", " 0.06077892333269119,\n", " -0.010921905748546124,\n", " -0.001620330847799778,\n", " -0.12291135638952255,\n", " -0.08137033134698868,\n", " -0.02304573357105255,\n", " -0.050796713680028915,\n", " -0.11667686700820923,\n", " 0.02522158995270729,\n", " -0.01985991559922695,\n", " 0.049684591591358185,\n", " -0.013690274208784103,\n", " 0.004341213498264551,\n", " -0.014368357136845589,\n", " 0.04770715534687042,\n", " 0.037185005843639374,\n", " 0.022829899564385414,\n", " -0.04654858633875847,\n", " 0.0693684071302414,\n", " -0.02132507786154747,\n", " 0.02957010269165039,\n", " 0.0009863555897027254,\n", " -0.03321843594312668,\n", " 0.04180164635181427,\n", " -0.005182781256735325,\n", " -0.020640278235077858,\n", " 0.04860810935497284,\n", " -0.0413326658308506,\n", " -0.0053645954467356205,\n", " -0.02189501002430916,\n", " 0.021270258352160454,\n", " 0.0184609554708004,\n", " -0.03659109026193619,\n", " -0.057284750044345856,\n", " -0.0034459319431334734,\n", " -0.04625871405005455,\n", " 0.05134567245841026,\n", " 0.025934113189578056,\n", " 0.038592562079429626,\n", " 0.06607186794281006,\n", " -0.01730247400701046,\n", " -0.010597990825772285,\n", " 0.022652020677924156,\n", " 0.03672371432185173,\n", " 0.03584642335772514,\n", " 0.012036608532071114,\n", " 0.03178581967949867,\n", " -0.12105194479227066,\n", " -1.5130833475041072e-08,\n", " 0.0068660578690469265,\n", " -0.027710307389497757,\n", " 0.08934704959392548,\n", " 0.07790021598339081,\n", " 0.0640479251742363,\n", " 0.03166112303733826,\n", " -0.07114409655332565,\n", " -0.04326840490102768,\n", " -0.03240787237882614,\n", " -0.021455857902765274,\n", " -0.008305346593260765,\n", " 0.03806023672223091,\n", " -0.00011358146002748981,\n", " 0.012233726680278778,\n", " 0.12013909220695496,\n", " 0.030236748978495598,\n", " -0.05191425606608391,\n", " -0.01144399493932724,\n", " -0.04784315079450607,\n", " -0.07834552973508835,\n", " 0.03221035376191139,\n", " -0.025861673057079315,\n", " -0.002064632950350642,\n", " -0.02923082374036312,\n", " -0.01997469924390316,\n", " -0.03496573492884636,\n", " -0.014112590812146664,\n", " 0.03993980586528778,\n", " -0.06896212697029114,\n", " 0.055890411138534546,\n", " -0.012570591643452644,\n", " 0.18196915090084076,\n", " -0.012776498682796955,\n", " -0.0011513171484693885,\n", " 0.02812478132545948,\n", " 0.011322981677949429,\n", " 0.0462803840637207,\n", " 0.016169818118214607,\n", " 0.026298904791474342,\n", " -0.05404868721961975,\n", " -0.026807114481925964,\n", " 0.06764838844537735,\n", " 0.010659311898052692,\n", " -0.1292618364095688,\n", " 0.06938319653272629,\n", " 0.030720528215169907,\n", " 0.03887750208377838,\n", " -0.14078199863433838,\n", " 0.05191195756196976,\n", " -0.06790664047002792,\n", " -0.029856646433472633,\n", " 0.017362020909786224,\n", " 0.08236221969127655,\n", " 0.11180814355611801,\n", " 0.0989338606595993,\n", " 0.057195451110601425,\n", " 0.015903165563941002,\n", " -0.04091334342956543,\n", " -0.012436420656740665,\n", " 0.020249128341674805,\n", " 0.06743747740983963,\n", " 0.039333511143922806,\n", " 0.05208098143339157,\n", " -0.01940511167049408]"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["embeddings.embed_query(\"hello <PERSON>\")"]}, {"cell_type": "code", "execution_count": 10, "id": "da366a6a", "metadata": {}, "outputs": [], "source": ["from sklearn.metrics.pairwise import cosine_similarity"]}, {"cell_type": "code", "execution_count": null, "id": "30b09b35", "metadata": {}, "outputs": [], "source": ["documents=[\"what is a capital of USA?\",\n", "           \"Who is a president of USA?\",\n", "           \"Who is a prime minister of India?\"]"]}, {"cell_type": "code", "execution_count": 12, "id": "77b4d8e8", "metadata": {}, "outputs": [], "source": ["my_query=\"<PERSON><PERSON><PERSON> mod<PERSON> is prime minister of india?\""]}, {"cell_type": "code", "execution_count": 14, "id": "9ee7cad4", "metadata": {}, "outputs": [], "source": ["document_embedding=embeddings.embed_documents(documents)"]}, {"cell_type": "code", "execution_count": 15, "id": "afd4456c", "metadata": {}, "outputs": [{"data": {"text/plain": ["[[0.11998696625232697,\n", "  -0.021302612498402596,\n", "  -0.04288085177540779,\n", "  0.06645581871271133,\n", "  -0.06435241550207138,\n", "  -0.04424869641661644,\n", "  0.02240842767059803,\n", "  -0.04987306892871857,\n", "  -0.023437613621354103,\n", "  -0.03397210314869881,\n", "  -0.014048037119209766,\n", "  -0.06065928936004639,\n", "  -0.0039068227633833885,\n", "  -0.017782099545001984,\n", "  -0.04797104746103287,\n", "  -0.06668158620595932,\n", "  0.004103198181837797,\n", "  -0.013092797249555588,\n", "  0.04439776763319969,\n", "  0.02235068380832672,\n", "  0.009459559805691242,\n", "  -0.020564520731568336,\n", "  -0.0003356117522343993,\n", "  -0.005685788579285145,\n", "  0.05558694899082184,\n", "  0.02512318082153797,\n", "  -0.0028171385638415813,\n", "  0.008758987300097942,\n", "  0.003255277406424284,\n", "  -0.015963422134518623,\n", "  0.014263655990362167,\n", "  -0.11220847070217133,\n", "  0.08968555927276611,\n", "  -0.03108377195894718,\n", "  -0.024223867803812027,\n", "  0.006152097601443529,\n", "  0.08058716356754303,\n", "  0.018250001594424248,\n", "  0.05568313971161842,\n", "  0.016702702268958092,\n", "  0.01589600369334221,\n", "  0.00034111147397197783,\n", "  0.0747237503528595,\n", "  0.08334598690271378,\n", "  -0.027716998010873795,\n", "  0.08709191530942917,\n", "  -0.02661431021988392,\n", "  0.09546807408332825,\n", "  0.04845811799168587,\n", "  -0.0005422867252491415,\n", "  0.07441717386245728,\n", "  0.014402857050299644,\n", "  -0.07961247116327286,\n", "  0.04607513174414635,\n", "  0.016927938908338547,\n", "  0.05119185149669647,\n", "  0.0087897889316082,\n", "  0.030947254970669746,\n", "  -0.019982177764177322,\n", "  -0.025256164371967316,\n", "  -0.08199966698884964,\n", "  -0.05898098275065422,\n", "  0.05177389457821846,\n", "  0.053160231560468674,\n", "  0.10308033972978592,\n", "  0.005394305568188429,\n", "  -0.018764346837997437,\n", "  0.010242796503007412,\n", "  -0.03563680872321129,\n", "  -0.060813188552856445,\n", "  0.02357766032218933,\n", "  -0.02800404280424118,\n", "  -0.02919022925198078,\n", "  0.027006465941667557,\n", "  -0.011239923536777496,\n", "  -0.09734894335269928,\n", "  -0.03591139242053032,\n", "  0.014033454470336437,\n", "  0.005382990930229425,\n", "  0.0978318378329277,\n", "  0.017041189596056938,\n", "  0.03076236881315708,\n", "  -0.026713574305176735,\n", "  0.027620773762464523,\n", "  -0.06221678480505943,\n", "  0.005113115534186363,\n", "  -0.011211710050702095,\n", "  0.009328685700893402,\n", "  -0.02914278581738472,\n", "  -0.023446563631296158,\n", "  -0.039755720645189285,\n", "  -0.015881983563303947,\n", "  -0.018512463197112083,\n", "  0.01994551531970501,\n", "  -0.10144564509391785,\n", "  0.027255859225988388,\n", "  -0.04012368991971016,\n", "  0.049070388078689575,\n", "  -0.06456653028726578,\n", "  0.030942175537347794,\n", "  0.11494258791208267,\n", "  0.027569442987442017,\n", "  0.016571231186389923,\n", "  -0.044114574790000916,\n", "  -0.006297423038631678,\n", "  0.09714550524950027,\n", "  0.004223036579787731,\n", "  -0.01608806848526001,\n", "  0.006996705662459135,\n", "  -0.011088456958532333,\n", "  -0.053852733224630356,\n", "  -0.019057130441069603,\n", "  -0.0952088013291359,\n", "  -0.019488496705889702,\n", "  0.001285214559175074,\n", "  -0.02081115171313286,\n", "  0.06137340888381004,\n", "  -0.05593278631567955,\n", "  -0.008794056251645088,\n", "  -0.0882732942700386,\n", "  -0.04165446013212204,\n", "  -0.01348904985934496,\n", "  0.003294649301096797,\n", "  -0.04499213397502899,\n", "  -0.03682854399085045,\n", "  -0.06178353354334831,\n", "  -0.08947035670280457,\n", "  -9.156046721409726e-33,\n", "  -0.054972823709249496,\n", "  -0.06403448432683945,\n", "  0.028229136019945145,\n", "  0.10220038890838623,\n", "  -0.07040510326623917,\n", "  0.0021619421895593405,\n", "  0.011609779670834541,\n", "  -0.051201723515987396,\n", "  -0.028828194364905357,\n", "  0.02596738189458847,\n", "  0.0863255113363266,\n", "  -0.11102858930826187,\n", "  -0.02884851209819317,\n", "  -0.02724475972354412,\n", "  0.09008240699768066,\n", "  0.03271495923399925,\n", "  0.016775503754615784,\n", "  0.0038062355015426874,\n", "  -0.011711236089468002,\n", "  -0.028155216947197914,\n", "  -0.012796219438314438,\n", "  0.025029955431818962,\n", "  -0.011314568109810352,\n", "  -0.05540704354643822,\n", "  -0.004834712482988834,\n", "  0.019063103944063187,\n", "  -0.06412197649478912,\n", "  0.04209174960851669,\n", "  0.07601164281368256,\n", "  -0.006603138521313667,\n", "  0.01712045446038246,\n", "  0.0969165563583374,\n", "  -0.03596673160791397,\n", "  0.05462392047047615,\n", "  0.04372861981391907,\n", "  -0.009087654761970043,\n", "  0.010597591288387775,\n", "  0.014450720511376858,\n", "  0.03351619467139244,\n", "  0.0036251998972147703,\n", "  0.020228814333677292,\n", "  -0.010260776616632938,\n", "  -0.05028516426682472,\n", "  0.10271774977445602,\n", "  0.09340399503707886,\n", "  0.02400926873087883,\n", "  -0.028630632907152176,\n", "  0.05638191103935242,\n", "  0.0631253644824028,\n", "  -0.03544722869992256,\n", "  0.023302847519516945,\n", "  -0.012664474546909332,\n", "  -0.024965358898043633,\n", "  -0.06517226994037628,\n", "  -0.01916990429162979,\n", "  0.021117078140378,\n", "  -0.024007458239793777,\n", "  0.014949454925954342,\n", "  -0.00015744719712529331,\n", "  0.0721564069390297,\n", "  -0.05955564230680466,\n", "  0.023409564048051834,\n", "  -0.023150086402893066,\n", "  0.052084267139434814,\n", "  -0.04306073859333992,\n", "  0.044143419712781906,\n", "  0.011553904972970486,\n", "  0.07138045877218246,\n", "  0.02533499151468277,\n", "  0.09426745772361755,\n", "  0.0463494211435318,\n", "  -0.03836066275835037,\n", "  0.019978376105427742,\n", "  0.15931813418865204,\n", "  0.027037108317017555,\n", "  0.03974718973040581,\n", "  0.07816359400749207,\n", "  -0.022662002593278885,\n", "  -0.025583364069461823,\n", "  -0.00499744201079011,\n", "  -0.08533387631177902,\n", "  -0.007994640618562698,\n", "  -0.07394948601722717,\n", "  0.05884123593568802,\n", "  0.0033905599266290665,\n", "  -0.026999637484550476,\n", "  -0.01361666340380907,\n", "  -0.037664081901311874,\n", "  0.008392956107854843,\n", "  -0.0938723236322403,\n", "  -0.07798036187887192,\n", "  -0.03650474175810814,\n", "  -0.000820544024463743,\n", "  -0.02801808901131153,\n", "  -0.05580991506576538,\n", "  3.694726416112272e-33,\n", "  -0.039700329303741455,\n", "  -0.0688432976603508,\n", "  -0.09622582793235779,\n", "  0.024865590035915375,\n", "  -0.04642008617520332,\n", "  -0.09270819276571274,\n", "  0.05773226544260979,\n", "  0.0762796476483345,\n", "  -0.01737803965806961,\n", "  -0.023387379944324493,\n", "  -0.09621445089578629,\n", "  -0.09463774412870407,\n", "  0.010559589602053165,\n", "  0.0858987420797348,\n", "  0.05276215448975563,\n", "  0.04274160787463188,\n", "  0.08087329566478729,\n", "  -0.033289674669504166,\n", "  -0.05490601435303688,\n", "  -0.05833572521805763,\n", "  -0.07151156663894653,\n", "  -0.010309084318578243,\n", "  -0.05233817175030708,\n", "  -0.02406168356537819,\n", "  -0.025990523397922516,\n", "  -0.0034229399170726538,\n", "  -0.15603947639465332,\n", "  -0.06466249376535416,\n", "  0.037263136357069016,\n", "  0.044162459671497345,\n", "  0.0018318307120352983,\n", "  0.01733393780887127,\n", "  0.0008058698149397969,\n", "  0.0883171409368515,\n", "  -0.11817768216133118,\n", "  0.08700119704008102,\n", "  0.10761367529630661,\n", "  -0.04190738871693611,\n", "  0.024424001574516296,\n", "  0.035175301134586334,\n", "  -0.006735536269843578,\n", "  -0.017665861174464226,\n", "  0.051245179027318954,\n", "  0.1601032316684723,\n", "  0.013393953442573547,\n", "  0.037188999354839325,\n", "  0.02158500626683235,\n", "  0.0332622192800045,\n", "  -0.10955913364887238,\n", "  0.0035534477792680264,\n", "  -0.05687888711690903,\n", "  -0.029679974541068077,\n", "  -0.051232654601335526,\n", "  0.022056881338357925,\n", "  -0.03324586898088455,\n", "  0.0383015014231205,\n", "  -0.06304039061069489,\n", "  0.0502084344625473,\n", "  -0.01864635944366455,\n", "  -0.0407356359064579,\n", "  0.06648886948823929,\n", "  0.03390166535973549,\n", "  -0.05880076065659523,\n", "  0.08322609215974808,\n", "  -0.03807930275797844,\n", "  -0.01739654317498207,\n", "  -0.023120077326893806,\n", "  -0.0007573551265522838,\n", "  -0.022358981892466545,\n", "  0.014902672730386257,\n", "  0.1017540916800499,\n", "  0.03810650110244751,\n", "  -0.07623767852783203,\n", "  -0.031818877905607224,\n", "  0.030856015160679817,\n", "  0.054588109254837036,\n", "  0.09602288156747818,\n", "  -0.002542308298870921,\n", "  -0.0042855083011090755,\n", "  -0.00889503862708807,\n", "  -0.03379863128066063,\n", "  0.017221126705408096,\n", "  -0.024751340970396996,\n", "  -0.004159558564424515,\n", "  -0.020667634904384613,\n", "  0.10024771094322205,\n", "  0.016019804403185844,\n", "  -0.013062222860753536,\n", "  -0.04356406256556511,\n", "  0.02882602997124195,\n", "  -0.05865854397416115,\n", "  0.045596953481435776,\n", "  -0.07187685370445251,\n", "  -1.04565860965522e-05,\n", "  -0.09609279781579971,\n", "  -1.8241456345435836e-08,\n", "  0.0077049825340509415,\n", "  0.033716801553964615,\n", "  -0.038363248109817505,\n", "  -0.003997677471488714,\n", "  -0.06704895943403244,\n", "  0.027960527688264847,\n", "  0.03752293437719345,\n", "  -0.020240895450115204,\n", "  -0.012025879696011543,\n", "  0.022288888692855835,\n", "  -0.04146719351410866,\n", "  0.022146057337522507,\n", "  -0.09870468825101852,\n", "  -0.05988563597202301,\n", "  -0.05446998402476311,\n", "  -0.020455652847886086,\n", "  -0.02632518671452999,\n", "  0.15268896520137787,\n", "  -0.022961528971791267,\n", "  -0.005267810076475143,\n", "  -0.012651678174734116,\n", "  0.06099013611674309,\n", "  -0.04828798398375511,\n", "  -0.023014264181256294,\n", "  -0.03055313415825367,\n", "  0.0004315426922403276,\n", "  0.0881073921918869,\n", "  0.08983238786458969,\n", "  0.018686074763536453,\n", "  -0.0013373197289183736,\n", "  0.06827342510223389,\n", "  -0.021983081474900246,\n", "  -0.08434168994426727,\n", "  -0.10404720902442932,\n", "  -0.04002056643366814,\n", "  -0.018682466819882393,\n", "  0.010417776182293892,\n", "  -0.046657267957925797,\n", "  0.014693393371999264,\n", "  -0.06961742043495178,\n", "  0.028571873903274536,\n", "  0.07051137089729309,\n", "  0.007716563995927572,\n", "  0.0027026564348489046,\n", "  0.03960391506552696,\n", "  0.02409188821911812,\n", "  0.005649031605571508,\n", "  0.044894520193338394,\n", "  0.053987760096788406,\n", "  -0.019145509228110313,\n", "  -0.07533007115125656,\n", "  -0.03700661286711693,\n", "  -0.01290101371705532,\n", "  -0.01916763000190258,\n", "  0.046692490577697754,\n", "  -0.011744667775928974,\n", "  0.02203262411057949,\n", "  -0.008390863426029682,\n", "  0.01717657595872879,\n", "  -0.03677796199917793,\n", "  0.020029768347740173,\n", "  -0.019656473770737648,\n", "  0.11816302686929703,\n", "  -0.0009502584580332041],\n", " [0.004469342064112425,\n", "  -0.027498658746480942,\n", "  -0.00977585930377245,\n", "  -0.012373632751405239,\n", "  -0.05691331624984741,\n", "  0.003830495523288846,\n", "  0.04348495602607727,\n", "  -0.03791218250989914,\n", "  0.037437696009874344,\n", "  0.02398592419922352,\n", "  -0.045684315264225006,\n", "  -0.012056984007358551,\n", "  -0.012477620504796505,\n", "  -0.035696860402822495,\n", "  -0.05724524334073067,\n", "  0.0152716850861907,\n", "  -0.021860897541046143,\n", "  0.040924347937107086,\n", "  -0.0016749034402891994,\n", "  -0.01771586760878563,\n", "  0.02007203735411167,\n", "  0.008453267626464367,\n", "  -0.01685742661356926,\n", "  -0.04264039918780327,\n", "  -0.03666297346353531,\n", "  0.004272368270903826,\n", "  -0.06239110231399536,\n", "  0.005437563639134169,\n", "  -0.023398643359541893,\n", "  -0.06545373797416687,\n", "  0.026638302952051163,\n", "  -0.15412095189094543,\n", "  0.057510681450366974,\n", "  0.004463867284357548,\n", "  -0.041832298040390015,\n", "  0.00032363185891881585,\n", "  0.04182683676481247,\n", "  0.034356847405433655,\n", "  0.01939736120402813,\n", "  -0.02665267139673233,\n", "  0.030110733583569527,\n", "  -0.03846770152449608,\n", "  -0.002840758068487048,\n", "  0.028519732877612114,\n", "  0.028735538944602013,\n", "  0.04722310230135918,\n", "  -0.04085788130760193,\n", "  0.05194222554564476,\n", "  0.04742853343486786,\n", "  0.033178627490997314,\n", "  0.05065976455807686,\n", "  0.09667287021875381,\n", "  -0.007640664000064135,\n", "  0.05960814282298088,\n", "  0.09688058495521545,\n", "  -0.024632452055811882,\n", "  -0.0261796023696661,\n", "  -0.035709090530872345,\n", "  0.05933407321572304,\n", "  0.009056968614459038,\n", "  -0.05278283357620239,\n", "  -0.030397523194551468,\n", "  -0.0021270967554301023,\n", "  0.015888210386037827,\n", "  -0.001261359779164195,\n", "  -0.02746673859655857,\n", "  -0.042450420558452606,\n", "  -0.06096549332141876,\n", "  2.374351242906414e-05,\n", "  -0.048449594527482986,\n", "  0.018205808475613594,\n", "  0.006513216067105532,\n", "  0.0072801727801561356,\n", "  0.043107327073812485,\n", "  -0.04655849561095238,\n", "  -0.04039813578128815,\n", "  0.1031673327088356,\n", "  0.09295857697725296,\n", "  -0.017387166619300842,\n", "  0.09046398103237152,\n", "  0.0574321374297142,\n", "  0.027025170624256134,\n", "  0.012127042748034,\n", "  -0.005367864854633808,\n", "  0.03049640916287899,\n", "  0.08987094461917877,\n", "  -0.06374386698007584,\n", "  0.04163001850247383,\n", "  -0.07242907583713531,\n", "  -0.017799796536564827,\n", "  -0.05358025059103966,\n", "  -0.026078134775161743,\n", "  0.04221294820308685,\n", "  0.02007639780640602,\n", "  -0.05454004183411598,\n", "  0.032619867473840714,\n", "  -0.02222701720893383,\n", "  0.03154262900352478,\n", "  -0.11964031308889389,\n", "  0.09139053523540497,\n", "  -0.0010479462798684835,\n", "  0.018369147554039955,\n", "  0.04886341840028763,\n", "  0.00927245244383812,\n", "  -0.009763090871274471,\n", "  0.11406508833169937,\n", "  -0.008152379654347897,\n", "  0.03773112595081329,\n", "  -0.022438110783696175,\n", "  -0.012340805493295193,\n", "  -0.0026801982894539833,\n", "  -0.026415696367621422,\n", "  -0.07049532234668732,\n", "  0.008802725933492184,\n", "  0.011517554521560669,\n", "  -0.13853120803833008,\n", "  0.01562708616256714,\n", "  -0.02431737445294857,\n", "  -0.029428763315081596,\n", "  -0.08484732359647751,\n", "  0.029539436101913452,\n", "  0.05707431212067604,\n", "  -0.01043439656496048,\n", "  -0.009558772668242455,\n", "  -0.018836278468370438,\n", "  -0.0732610747218132,\n", "  0.0042117019183933735,\n", "  -8.709712985770442e-33,\n", "  -0.05977107584476471,\n", "  -0.05801320821046829,\n", "  0.0930558294057846,\n", "  0.1033283919095993,\n", "  -0.08351823687553406,\n", "  0.0625547245144844,\n", "  0.02425001934170723,\n", "  -0.05324019491672516,\n", "  0.023736227303743362,\n", "  0.014534169808030128,\n", "  0.027958784252405167,\n", "  -0.04344823583960533,\n", "  -0.05420004948973656,\n", "  0.03203708678483963,\n", "  0.039060208946466446,\n", "  0.023688174784183502,\n", "  -0.026534387841820717,\n", "  0.0001835636649047956,\n", "  -0.02409256435930729,\n", "  0.00746091827750206,\n", "  0.01834827847778797,\n", "  0.05702926218509674,\n", "  0.035800155252218246,\n", "  -0.0036268809344619513,\n", "  0.0769299790263176,\n", "  -0.003018520073965192,\n", "  -0.03498946130275726,\n", "  -0.011447305791079998,\n", "  0.03428925573825836,\n", "  0.0008706361404620111,\n", "  0.0499199740588665,\n", "  0.00846536923199892,\n", "  -0.06608720123767853,\n", "  0.010103692300617695,\n", "  -0.02125057950615883,\n", "  -0.0782838836312294,\n", "  0.0025414112024009228,\n", "  -0.016806358471512794,\n", "  0.025666605681180954,\n", "  -0.04944858327507973,\n", "  0.006155289709568024,\n", "  0.020478812977671623,\n", "  0.03222515434026718,\n", "  0.0959387719631195,\n", "  0.04164478927850723,\n", "  -0.04236063361167908,\n", "  0.02767716720700264,\n", "  0.09538834542036057,\n", "  0.020276838913559914,\n", "  0.02859361656010151,\n", "  -0.018278466537594795,\n", "  0.04545004665851593,\n", "  0.0034076001029461622,\n", "  -0.053605105727910995,\n", "  -0.04804953187704086,\n", "  -0.07224269211292267,\n", "  0.03580353036522865,\n", "  0.016636157408356667,\n", "  -0.009972657077014446,\n", "  -0.005909627769142389,\n", "  -0.04499030485749245,\n", "  0.05281084403395653,\n", "  -0.0831037163734436,\n", "  0.10512281209230423,\n", "  -0.08673796057701111,\n", "  -0.0013459521578624845,\n", "  -0.040061283856630325,\n", "  -0.012231901288032532,\n", "  -0.013684049248695374,\n", "  0.022009005770087242,\n", "  0.07545480877161026,\n", "  -0.026423869654536247,\n", "  0.0801420509815216,\n", "  0.05197828263044357,\n", "  -0.023699700832366943,\n", "  0.06830336153507233,\n", "  0.07627585530281067,\n", "  -0.014677132479846478,\n", "  -0.05702490732073784,\n", "  -0.026448814198374748,\n", "  -0.14814290404319763,\n", "  0.01891108974814415,\n", "  0.1476442664861679,\n", "  0.033495087176561356,\n", "  -0.026811722666025162,\n", "  0.04746590554714203,\n", "  -0.009787014685571194,\n", "  -0.026799285784363747,\n", "  0.05799683555960655,\n", "  0.020957693457603455,\n", "  -0.09652025997638702,\n", "  0.007892548106610775,\n", "  0.04304855689406395,\n", "  -0.029415922239422798,\n", "  -0.041843537241220474,\n", "  3.98847363817513e-33,\n", "  -0.09012366086244583,\n", "  0.017528153955936432,\n", "  0.05968767777085304,\n", "  0.020845554769039154,\n", "  0.03597908839583397,\n", "  -0.001588430954143405,\n", "  0.04056580364704132,\n", "  0.03433563560247421,\n", "  -0.07661334425210953,\n", "  -0.10743927955627441,\n", "  -0.0013606632128357887,\n", "  0.017633143812417984,\n", "  0.05835267901420593,\n", "  0.04777019843459129,\n", "  0.044675540179014206,\n", "  0.030940553173422813,\n", "  0.06300907582044601,\n", "  -0.09718991816043854,\n", "  -0.07440067082643509,\n", "  -0.0802595391869545,\n", "  -0.09002853184938431,\n", "  0.12981311976909637,\n", "  -0.05098855867981911,\n", "  -0.01831679791212082,\n", "  -0.02893470972776413,\n", "  -0.07705865800380707,\n", "  -0.01204765122383833,\n", "  -0.05400954186916351,\n", "  -0.0395536869764328,\n", "  -0.04650038853287697,\n", "  -0.02529979683458805,\n", "  0.03259245306253433,\n", "  -0.03267150744795799,\n", "  0.0011610641377046704,\n", "  0.024263247847557068,\n", "  0.1178082674741745,\n", "  0.05214553698897362,\n", "  -0.060853149741888046,\n", "  0.07875119894742966,\n", "  0.02805781550705433,\n", "  0.03580367565155029,\n", "  -0.0072457315400242805,\n", "  0.0240035317838192,\n", "  0.055979203432798386,\n", "  -0.05674684792757034,\n", "  0.024000944569706917,\n", "  -0.06704973429441452,\n", "  0.0240129753947258,\n", "  -0.10639990866184235,\n", "  -0.026185153052210808,\n", "  -0.1371910125017166,\n", "  0.029531177133321762,\n", "  -0.031018273904919624,\n", "  -0.019901858642697334,\n", "  0.02520710788667202,\n", "  0.032090429216623306,\n", "  -0.07979322224855423,\n", "  -0.003880841191858053,\n", "  0.03841305151581764,\n", "  0.06600605696439743,\n", "  0.0023533152416348457,\n", "  -0.018673527985811234,\n", "  0.01801096647977829,\n", "  0.0030829720199108124,\n", "  -0.040401641279459,\n", "  -0.0055632502771914005,\n", "  -0.0502009242773056,\n", "  0.06584872305393219,\n", "  0.006999613717198372,\n", "  -0.027415847405791283,\n", "  0.065668024122715,\n", "  -0.03267471119761467,\n", "  -0.061098821461200714,\n", "  0.007584867533296347,\n", "  0.033374011516571045,\n", "  0.011376535519957542,\n", "  0.0011290829861536622,\n", "  -0.07854493707418442,\n", "  0.038897231221199036,\n", "  -0.017037199810147285,\n", "  -0.05103938281536102,\n", "  -0.03367668390274048,\n", "  -0.042997442185878754,\n", "  -0.009658451192080975,\n", "  -0.08602027595043182,\n", "  0.040909361094236374,\n", "  0.03654690086841583,\n", "  -0.08137556165456772,\n", "  -0.0008070933981798589,\n", "  -0.0030680992640554905,\n", "  -0.0168498195707798,\n", "  0.0040210215374827385,\n", "  -0.019711961969733238,\n", "  -0.07265707105398178,\n", "  -0.06724786758422852,\n", "  -1.7726717871369146e-08,\n", "  0.044746577739715576,\n", "  0.051992591470479965,\n", "  0.036084748804569244,\n", "  -0.01388600841164589,\n", "  -0.025977537035942078,\n", "  0.03686147555708885,\n", "  0.0025491476990282536,\n", "  -0.05319584161043167,\n", "  0.017580073326826096,\n", "  -0.05344828590750694,\n", "  0.06180750951170921,\n", "  -0.06145919859409332,\n", "  -0.016083665192127228,\n", "  -0.1020791083574295,\n", "  0.071685791015625,\n", "  -0.09449504315853119,\n", "  -0.03254198655486107,\n", "  0.17739038169384003,\n", "  -0.023790758103132248,\n", "  0.020986774936318398,\n", "  -0.027314938604831696,\n", "  0.053018756210803986,\n", "  -0.02861930988729,\n", "  -0.019824353978037834,\n", "  0.039636529982089996,\n", "  0.0010320702567696571,\n", "  0.07545534521341324,\n", "  0.0481998436152935,\n", "  -0.02504093013703823,\n", "  0.0760417953133583,\n", "  0.03878295421600342,\n", "  0.03520767763257027,\n", "  -0.1172402873635292,\n", "  -0.062459271401166916,\n", "  -0.004702999722212553,\n", "  0.0012660217471420765,\n", "  -0.008337642066180706,\n", "  -0.030735520645976067,\n", "  0.04755272716283798,\n", "  -0.07997917383909225,\n", "  -0.003836611518636346,\n", "  0.1628202646970749,\n", "  0.007661927957087755,\n", "  0.06032949313521385,\n", "  0.0041311271488666534,\n", "  0.009105099365115166,\n", "  0.04481964930891991,\n", "  0.019544808194041252,\n", "  0.027674101293087006,\n", "  0.037407178431749344,\n", "  -0.09479431807994843,\n", "  0.02268686331808567,\n", "  0.07733862847089767,\n", "  -0.04788723587989807,\n", "  0.03542131558060646,\n", "  -0.03728444129228592,\n", "  0.04707344248890877,\n", "  -0.0324430875480175,\n", "  0.020188776776194572,\n", "  -0.06740794330835342,\n", "  0.04438764229416847,\n", "  -0.023608727380633354,\n", "  0.10382762551307678,\n", "  0.005892746616154909],\n", " [0.023476378992199898,\n", "  0.026060013100504875,\n", "  -0.015785949304699898,\n", "  0.012368092313408852,\n", "  -0.0056069097481667995,\n", "  -0.01832295022904873,\n", "  0.10647313296794891,\n", "  0.03678399696946144,\n", "  -0.016545729711651802,\n", "  0.026793843135237694,\n", "  -0.03765823319554329,\n", "  -0.02812589518725872,\n", "  0.003954185638576746,\n", "  0.005263751372694969,\n", "  0.03652828931808472,\n", "  0.03620995208621025,\n", "  0.008976356126368046,\n", "  0.019555820152163506,\n", "  0.022173509001731873,\n", "  -0.08445638418197632,\n", "  0.003121080808341503,\n", "  0.042081113904714584,\n", "  0.002619517967104912,\n", "  -0.05357525497674942,\n", "  0.018115468323230743,\n", "  -0.0008681723847985268,\n", "  0.017705289646983147,\n", "  -0.04961323365569115,\n", "  -0.04173879697918892,\n", "  -0.029424740001559258,\n", "  0.04163023456931114,\n", "  -0.032393939793109894,\n", "  -0.008378901518881321,\n", "  0.0261092446744442,\n", "  -0.016940562054514885,\n", "  0.007789130788296461,\n", "  -0.01900884509086609,\n", "  0.09718157351016998,\n", "  0.06793887913227081,\n", "  -0.036248039454221725,\n", "  0.08734069019556046,\n", "  -0.043627671897411346,\n", "  -0.006657057907432318,\n", "  -0.03032776527106762,\n", "  0.05864843726158142,\n", "  -0.0301367100328207,\n", "  0.01800273358821869,\n", "  0.006007435265928507,\n", "  -0.0030188693199306726,\n", "  -0.012908056378364563,\n", "  -0.0019289611373096704,\n", "  -0.009466304443776608,\n", "  -0.03725098446011543,\n", "  -0.03482099995017052,\n", "  0.0815831869840622,\n", "  -0.07474132627248764,\n", "  -0.01313719991594553,\n", "  -0.06022803857922554,\n", "  -0.0015850818017497659,\n", "  0.009354908019304276,\n", "  -0.09515859931707382,\n", "  0.045292552560567856,\n", "  -0.10166804492473602,\n", "  0.021471034735441208,\n", "  0.05603066831827164,\n", "  -0.052113596349954605,\n", "  0.021393077448010445,\n", "  -0.05932000279426575,\n", "  0.019517406821250916,\n", "  -0.08471015095710754,\n", "  -0.025610949844121933,\n", "  -0.007060110103338957,\n", "  0.05890178680419922,\n", "  -0.06056269630789757,\n", "  -0.05084026977419853,\n", "  -0.11082424968481064,\n", "  0.026688000187277794,\n", "  0.1547241508960724,\n", "  -0.04932757839560509,\n", "  0.023299578577280045,\n", "  -0.0021739795338362455,\n", "  -0.0017007091082632542,\n", "  0.052584704011678696,\n", "  0.04685795679688454,\n", "  0.011226720176637173,\n", "  0.04814862087368965,\n", "  -0.009334935806691647,\n", "  0.028928179293870926,\n", "  -0.09821082651615143,\n", "  -0.04335053265094757,\n", "  0.012640418484807014,\n", "  0.053415752947330475,\n", "  0.08357839286327362,\n", "  0.06783165782690048,\n", "  0.021751435473561287,\n", "  0.049490902572870255,\n", "  0.0054468391463160515,\n", "  -0.0777365118265152,\n", "  -0.0993305966258049,\n", "  0.10112657397985458,\n", "  -0.04313785582780838,\n", "  0.024749521166086197,\n", "  -0.036782123148441315,\n", "  0.01682918332517147,\n", "  -0.07732316851615906,\n", "  0.00995826069265604,\n", "  -0.036544304341077805,\n", "  0.027184253558516502,\n", "  0.0381038561463356,\n", "  -0.0003052283136639744,\n", "  -0.012155238538980484,\n", "  0.006757750175893307,\n", "  -0.013191848993301392,\n", "  -0.0015812134370207787,\n", "  0.028808457776904106,\n", "  -0.05871134251356125,\n", "  -0.022927582263946533,\n", "  0.05630772188305855,\n", "  0.02351466380059719,\n", "  -0.0631953775882721,\n", "  0.0025089485570788383,\n", "  0.026741931214928627,\n", "  -0.0478314533829689,\n", "  -0.005816248711198568,\n", "  -0.0527561791241169,\n", "  0.029089411720633507,\n", "  -0.03938305005431175,\n", "  -5.5603990507084186e-33,\n", "  -0.028821734711527824,\n", "  -0.00401094276458025,\n", "  0.11976275593042374,\n", "  0.030322708189487457,\n", "  -0.07163259387016296,\n", "  0.02209780178964138,\n", "  0.01313110813498497,\n", "  -0.05877573415637016,\n", "  0.011134100146591663,\n", "  -0.023268161341547966,\n", "  0.0026834008749574423,\n", "  -0.13793528079986572,\n", "  0.01155775785446167,\n", "  -0.10132278501987457,\n", "  0.009783882647752762,\n", "  -0.018680257722735405,\n", "  -0.03804534301161766,\n", "  0.0544155016541481,\n", "  -0.026246223598718643,\n", "  0.03262948617339134,\n", "  -0.0383576937019825,\n", "  0.06473404169082642,\n", "  0.003700491040945053,\n", "  -0.012489723972976208,\n", "  0.08153314888477325,\n", "  0.034987062215805054,\n", "  0.12670980393886566,\n", "  0.012453824281692505,\n", "  0.09247192740440369,\n", "  0.031662311404943466,\n", "  0.06110464036464691,\n", "  0.03605930507183075,\n", "  -0.07316338270902634,\n", "  -0.007308960892260075,\n", "  -0.06503912806510925,\n", "  -0.021586215123534203,\n", "  -0.05726834759116173,\n", "  -0.050766993314027786,\n", "  -0.04006871581077576,\n", "  0.0064923991449177265,\n", "  -0.08833300322294235,\n", "  -0.014650012366473675,\n", "  0.02380673959851265,\n", "  0.0001484007661929354,\n", "  -0.049520134925842285,\n", "  -0.04043997451663017,\n", "  0.02447165921330452,\n", "  0.029639750719070435,\n", "  -0.005035860929638147,\n", "  0.010038594715297222,\n", "  -0.03264918923377991,\n", "  0.03613157197833061,\n", "  -0.037653300911188126,\n", "  -0.015912160277366638,\n", "  0.005933747626841068,\n", "  -0.01600903458893299,\n", "  0.027980204671621323,\n", "  0.00829336978495121,\n", "  0.04762739688158035,\n", "  0.020444724708795547,\n", "  0.029620295390486717,\n", "  -0.03958539292216301,\n", "  -0.08469105511903763,\n", "  0.06526084244251251,\n", "  -0.009513042867183685,\n", "  0.08011804521083832,\n", "  0.03558572754263878,\n", "  -0.042832717299461365,\n", "  0.03807205706834793,\n", "  0.010694845579564571,\n", "  0.026005707681179047,\n", "  0.04066312313079834,\n", "  0.08698660880327225,\n", "  0.02172996662557125,\n", "  -0.09914804250001907,\n", "  0.030298568308353424,\n", "  0.01949065364897251,\n", "  -0.017028558999300003,\n", "  -0.07961154729127884,\n", "  0.11495207250118256,\n", "  -0.07255538552999496,\n", "  0.03715075924992561,\n", "  0.07019145786762238,\n", "  0.0005662177572958171,\n", "  0.015158838592469692,\n", "  -0.02615669183433056,\n", "  -0.06655778735876083,\n", "  0.001688468037173152,\n", "  0.036312445998191833,\n", "  -0.051381032913923264,\n", "  -0.017367295920848846,\n", "  -0.07924410700798035,\n", "  0.018480977043509483,\n", "  0.011644404381513596,\n", "  -0.02160884253680706,\n", "  3.23188139294775e-33,\n", "  -0.02800261788070202,\n", "  0.044395118951797485,\n", "  -0.04319507256150246,\n", "  0.05931420251727104,\n", "  0.059319525957107544,\n", "  -0.023215962573885918,\n", "  0.0475158728659153,\n", "  0.023708121851086617,\n", "  -0.03091752529144287,\n", "  0.04697668179869652,\n", "  0.045439399778842926,\n", "  0.0329202264547348,\n", "  0.0778098925948143,\n", "  0.03897550329566002,\n", "  0.01913769543170929,\n", "  0.09472724050283432,\n", "  0.01127066183835268,\n", "  -0.018234901130199432,\n", "  -0.07204948365688324,\n", "  -0.016024498268961906,\n", "  -0.0673757940530777,\n", "  0.05741696432232857,\n", "  -0.015590182505548,\n", "  -0.03912941366434097,\n", "  -0.05077555030584335,\n", "  -0.02638450637459755,\n", "  -0.03461344167590141,\n", "  -0.03864889219403267,\n", "  -0.10995271801948547,\n", "  -0.036555785685777664,\n", "  0.009323879145085812,\n", "  0.023250102996826172,\n", "  -0.15205493569374084,\n", "  -0.09219356626272202,\n", "  -0.09254445880651474,\n", "  0.05535365268588066,\n", "  -0.023792976513504982,\n", "  -0.04614255949854851,\n", "  0.014154760167002678,\n", "  0.10270805656909943,\n", "  -0.055543892085552216,\n", "  0.047026216983795166,\n", "  0.16017091274261475,\n", "  0.05073773115873337,\n", "  -0.03434992954134941,\n", "  -0.0331784188747406,\n", "  0.02898279018700123,\n", "  0.01456615049391985,\n", "  0.011896529234945774,\n", "  -0.023827124387025833,\n", "  -0.0658361092209816,\n", "  0.02831834927201271,\n", "  0.01624942384660244,\n", "  -0.08254895359277725,\n", "  0.0994831994175911,\n", "  -0.023949194699525833,\n", "  -0.036586713045835495,\n", "  0.08967595547437668,\n", "  0.026866445317864418,\n", "  -0.041310399770736694,\n", "  0.028685247525572777,\n", "  -0.026643823832273483,\n", "  0.08257484436035156,\n", "  0.06146594509482384,\n", "  -0.06226912513375282,\n", "  0.038772620260715485,\n", "  -0.026122620329260826,\n", "  0.03822508454322815,\n", "  0.0882810428738594,\n", "  -0.02496788650751114,\n", "  0.09504653513431549,\n", "  -0.03435882180929184,\n", "  -0.03370713070034981,\n", "  0.05825004726648331,\n", "  -0.0827462300658226,\n", "  0.04262964427471161,\n", "  0.028552645817399025,\n", "  0.045205943286418915,\n", "  0.018350845202803612,\n", "  0.04858960211277008,\n", "  0.036365821957588196,\n", "  -0.018980588763952255,\n", "  -0.02379535511136055,\n", "  0.007191062904894352,\n", "  -0.09542272239923477,\n", "  -0.02368667535483837,\n", "  0.11956918984651566,\n", "  -0.04317730665206909,\n", "  0.017780635505914688,\n", "  0.01629428192973137,\n", "  -0.020718447864055634,\n", "  -0.011182972230017185,\n", "  0.07271867245435715,\n", "  -0.020255940034985542,\n", "  0.04114489257335663,\n", "  -1.650003866870975e-08,\n", "  0.0028417303692549467,\n", "  -0.07381509244441986,\n", "  -0.08223208039999008,\n", "  -0.02718840353190899,\n", "  -0.0022237454541027546,\n", "  0.034639615565538406,\n", "  0.007823853753507137,\n", "  -0.051174063235521317,\n", "  -0.010182643309235573,\n", "  -0.04233632609248161,\n", "  0.07594486325979233,\n", "  -0.09527856111526489,\n", "  -0.020139707252383232,\n", "  -0.04657416045665741,\n", "  0.054177213460206985,\n", "  -0.054825350642204285,\n", "  0.01390256080776453,\n", "  0.08158878237009048,\n", "  -0.031879305839538574,\n", "  -0.062093887478113174,\n", "  -0.03565163537859917,\n", "  0.013494450598955154,\n", "  0.03864115849137306,\n", "  -0.021908627822995186,\n", "  0.02295498363673687,\n", "  -0.014119510538876057,\n", "  0.021972032263875008,\n", "  0.006833675317466259,\n", "  0.012270529754459858,\n", "  0.0735645741224289,\n", "  -0.05826162174344063,\n", "  0.08079786598682404,\n", "  -0.0484686903655529,\n", "  -0.11860935389995575,\n", "  -0.061331819742918015,\n", "  0.056348394602537155,\n", "  0.059746239334344864,\n", "  -0.025815319269895554,\n", "  0.08199889212846756,\n", "  0.003852940397337079,\n", "  0.008213819935917854,\n", "  0.060123905539512634,\n", "  -0.012843984179198742,\n", "  0.07298875600099564,\n", "  0.04853453487157822,\n", "  0.02862631157040596,\n", "  0.0865892544388771,\n", "  0.008095875382423401,\n", "  -0.05719440057873726,\n", "  -0.12227381765842438,\n", "  -0.0891578271985054,\n", "  0.0683383047580719,\n", "  0.0948844775557518,\n", "  0.013167917728424072,\n", "  0.00623146491125226,\n", "  0.046013545244932175,\n", "  -0.06080910935997963,\n", "  -0.019121335819363594,\n", "  -0.0036292977165430784,\n", "  -0.057320572435855865,\n", "  0.03411716967821121,\n", "  -0.03172498941421509,\n", "  0.02960282564163208,\n", "  -0.03145111724734306]]"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["document_embedding"]}, {"cell_type": "code", "execution_count": 16, "id": "469b930d", "metadata": {}, "outputs": [], "source": ["query_embedding=embeddings.embed_query(my_query)"]}, {"cell_type": "code", "execution_count": 22, "id": "6b488961", "metadata": {}, "outputs": [{"data": {"text/plain": ["384"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["len(query_embedding)"]}, {"cell_type": "code", "execution_count": 18, "id": "eeb8b4ec", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[0.11756671, 0.34324567, 0.81413238]])"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["cosine_similarity([query_embedding],document_embedding)"]}, {"cell_type": "code", "execution_count": 19, "id": "30c83915", "metadata": {}, "outputs": [], "source": ["from sklearn.metrics.pairwise import euclidean_distances"]}, {"cell_type": "code", "execution_count": 20, "id": "63ace3af", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[1.3284828 , 1.14608406, 0.60970095]])"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["euclidean_distances([query_embedding], document_embedding)"]}, {"cell_type": "markdown", "id": "a4d93966", "metadata": {}, "source": ["| Metric            | Similarity Score Range | Behavior                              |\n", "| ----------------- | ---------------------- | ------------------------------------- |\n", "| Cosine Similarity | \\[-1, 1]               | Focuses on angle only |\n", "| L2 Distance       | \\[0, ∞)                | Focuses on **magnitude + direction**  |\n"]}, {"cell_type": "code", "execution_count": 21, "id": "81e077ca", "metadata": {}, "outputs": [], "source": ["import faiss\n", "from langchain_community.vectorstores import FAISS\n", "from langchain_community.docstore.in_memory import InMemoryDocstore"]}, {"cell_type": "code", "execution_count": 23, "id": "67275a65", "metadata": {}, "outputs": [], "source": ["index=faiss.IndexFlatL2(384)\n"]}, {"cell_type": "markdown", "id": "eba7a723", "metadata": {}, "source": []}, {"cell_type": "code", "execution_count": 24, "id": "69952612", "metadata": {}, "outputs": [{"data": {"text/plain": ["<faiss.swigfaiss_avx2.IndexFlatL2; proxy of <Swig Object of type 'faiss::IndexFlatL2 *' at 0x000001F56CF66490> >"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["index"]}, {"cell_type": "code", "execution_count": 25, "id": "392701d3", "metadata": {}, "outputs": [], "source": ["vector_store=FAISS(\n", "    embedding_function=embeddings,\n", "    index=index,\n", "    docstore=InMemoryDocstore(),\n", "    index_to_docstore_id={},\n", ")\n"]}, {"cell_type": "code", "execution_count": 26, "id": "ffefa439", "metadata": {}, "outputs": [{"data": {"text/plain": ["['f7674273-8218-43c1-b603-9e989c42ad50',\n", " '7aa9a5f7-57c1-45d1-ac23-1dd14dbca27e',\n", " '2bc0db44-373d-4b33-837d-66d82b28e3d4']"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["vector_store.add_texts([\"AI is future\",\"AI is powerful\",\"Dogs are cute\"])"]}, {"cell_type": "code", "execution_count": 27, "id": "db6f02a5", "metadata": {}, "outputs": [{"data": {"text/plain": ["{0: 'f7674273-8218-43c1-b603-9e989c42ad50',\n", " 1: '7aa9a5f7-57c1-45d1-ac23-1dd14dbca27e',\n", " 2: '2bc0db44-373d-4b33-837d-66d82b28e3d4'}"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["vector_store.index_to_docstore_id"]}, {"cell_type": "code", "execution_count": 32, "id": "5cef8ac2", "metadata": {}, "outputs": [], "source": ["results = vector_store.similarity_search(\"Tell me about AI\", k=3)"]}, {"cell_type": "code", "execution_count": 33, "id": "5f24edef", "metadata": {}, "outputs": [{"data": {"text/plain": ["[Document(id='7aa9a5f7-57c1-45d1-ac23-1dd14dbca27e', metadata={}, page_content='AI is powerful'),\n", " Document(id='f7674273-8218-43c1-b603-9e989c42ad50', metadata={}, page_content='AI is future'),\n", " Document(id='2bc0db44-373d-4b33-837d-66d82b28e3d4', metadata={}, page_content='Dogs are cute')]"]}, "execution_count": 33, "metadata": {}, "output_type": "execute_result"}], "source": ["results\n"]}, {"cell_type": "markdown", "id": "fecf445c", "metadata": {}, "source": ["| Feature               | `Flat`                | `IVF` (Inverted File Index)        | `HNSW` (Graph-based Index)          |\n", "| --------------------- | --------------------- | ---------------------------------- | ----------------------------------- |\n", "| Type of Search     | Exact                 | Approximate (cluster-based)        | Approximate (graph-based traversal) |\n", "| Speed               | Slow (linear scan)    | Fast (search only in top clusters) | Very Fast (graph walk)              |\n"]}, {"cell_type": "markdown", "id": "375b2f1c", "metadata": {}, "source": ["| Dataset Size              | Recommended Index                 |\n", "| ------------------------- | --------------------------------- |\n", "| UPTO 1L                     | `IndexFlatL2` or `IndexFlatIP`    |\n", "| UPTO 1M                  | `IndexIVFFlat` or `IndexHNSWFlat` |\n", "| > 1M                      | `IndexIVFPQ` or `IndexHNSWFlat`   |\n"]}, {"cell_type": "code", "execution_count": 34, "id": "fb691fb8", "metadata": {}, "outputs": [], "source": ["# from uuid import uuid4\n", "from langchain_core.documents import Document\n", "\n", "document_1 = Document(\n", "    page_content=\"I had chocolate chip pancakes and scrambled eggs for breakfast this morning.\",\n", "    metadata={\"source\": \"tweet\"},\n", ")\n", "\n", "document_2 = Document(\n", "    page_content=\"The weather forecast for tomorrow is cloudy and overcast, with a high of 62 degrees.\",\n", "    metadata={\"source\": \"news\"},\n", ")\n", "\n", "document_3 = Document(\n", "    page_content=\"Building an exciting new project with <PERSON><PERSON><PERSON><PERSON> - come check it out!\",\n", "    metadata={\"source\": \"tweet\"},\n", ")\n", "\n", "document_4 = Document(\n", "    page_content=\"Robbers broke into the city bank and stole $1 million in cash.\",\n", "    metadata={\"source\": \"news\"},\n", ")\n", "\n", "document_5 = Document(\n", "    page_content=\"Wow! That was an amazing movie. I can't wait to see it again.\",\n", "    metadata={\"source\": \"tweet\"},\n", ")\n", "\n", "document_6 = Document(\n", "    page_content=\"Is the new iPhone worth the price? Read this review to find out.\",\n", "    metadata={\"source\": \"website\"},\n", ")\n", "\n", "document_7 = Document(\n", "    page_content=\"The top 10 soccer players in the world right now.\",\n", "    metadata={\"source\": \"website\"},\n", ")\n", "\n", "document_8 = Document(\n", "    page_content=\"LangGraph is the best framework for building stateful, agentic applications!\",\n", "    metadata={\"source\": \"tweet\"},\n", ")\n", "\n", "document_9 = Document(\n", "    page_content=\"The stock market is down 500 points today due to fears of a recession.\",\n", "    metadata={\"source\": \"news\"},\n", ")\n", "\n", "document_10 = Document(\n", "    page_content=\"I have a bad feeling I am going to get deleted :(\",\n", "    metadata={\"source\": \"tweet\"},\n", ")\n", "\n", "documents = [\n", "    document_1,\n", "    document_2,\n", "    document_3,\n", "    document_4,\n", "    document_5,\n", "    document_6,\n", "    document_7,\n", "    document_8,\n", "    document_9,\n", "    document_10,\n", "]"]}, {"cell_type": "code", "execution_count": 35, "id": "fcf5c512", "metadata": {}, "outputs": [], "source": ["index=faiss.IndexFlatIP(384)\n", "vector_store=FAISS(\n", "    embedding_function=embeddings,\n", "    index=index,\n", "    docstore=InMemoryDocstore(),\n", "    index_to_docstore_id={},\n", ")"]}, {"cell_type": "code", "execution_count": 36, "id": "4a7430e3", "metadata": {}, "outputs": [{"data": {"text/plain": ["['2bb6e02d-8272-44e7-8f8f-fa5813fd1e86',\n", " 'c0451cb1-e589-449f-af7b-629fbb911796',\n", " 'e6ade172-ce20-4179-861e-147cf6e1c3d5',\n", " 'b6f61b89-bc3e-4c83-88ef-86d81fd526ef',\n", " 'b4c2983a-bcf2-4ba0-9182-4e68b530a9aa',\n", " 'bcc2046b-de7e-4891-a900-b73d3fe7c96c',\n", " 'f3fbd2f5-dd62-4bfc-b03f-02863a8bcc73',\n", " 'a6e1aa11-4850-438c-acef-78be27abb03a',\n", " '7e94c32f-30e2-4010-96d4-9d27258f3420',\n", " '85f4a46c-d5f6-4154-9685-1fdc071bf6cb']"]}, "execution_count": 36, "metadata": {}, "output_type": "execute_result"}], "source": ["vector_store.add_documents(documents=documents)"]}, {"cell_type": "code", "execution_count": 41, "id": "701ae4e5", "metadata": {}, "outputs": [{"data": {"text/plain": ["[Document(id='e6ade172-ce20-4179-861e-147cf6e1c3d5', metadata={'source': 'tweet'}, page_content='Building an exciting new project with Lang<PERSON>hain - come check it out!'),\n", " Document(id='a6e1aa11-4850-438c-acef-78be27abb03a', metadata={'source': 'tweet'}, page_content='LangGraph is the best framework for building stateful, agentic applications!')]"]}, "execution_count": 41, "metadata": {}, "output_type": "execute_result"}], "source": ["vector_store.similarity_search(\n", "    \"Lang<PERSON>hain provides abstractions to make working with LLMs easy\",\n", "    k=2 #hyperparameter\n", "    \n", ")"]}, {"cell_type": "code", "execution_count": 42, "id": "81f84ba4", "metadata": {}, "outputs": [{"data": {"text/plain": ["[Document(id='e6ade172-ce20-4179-861e-147cf6e1c3d5', metadata={'source': 'tweet'}, page_content='Building an exciting new project with Lang<PERSON>hain - come check it out!'),\n", " Document(id='a6e1aa11-4850-438c-acef-78be27abb03a', metadata={'source': 'tweet'}, page_content='LangGraph is the best framework for building stateful, agentic applications!'),\n", " Document(id='85f4a46c-d5f6-4154-9685-1fdc071bf6cb', metadata={'source': 'tweet'}, page_content='I have a bad feeling I am going to get deleted :('),\n", " Document(id='2bb6e02d-8272-44e7-8f8f-fa5813fd1e86', metadata={'source': 'tweet'}, page_content='I had chocolate chip pancakes and scrambled eggs for breakfast this morning.')]"]}, "execution_count": 42, "metadata": {}, "output_type": "execute_result"}], "source": ["vector_store.similarity_search(\n", "    \"Lang<PERSON>hain provides abstractions to make working with LLMs easy\",\n", "    #k=2 #hyperparameter,\n", "    filter={\"source\":{\"$eq\": \"tweet\"}}\n", "    \n", ")"]}, {"cell_type": "code", "execution_count": 44, "id": "30cc562b", "metadata": {}, "outputs": [], "source": ["result=vector_store.similarity_search(\n", "    \"Lang<PERSON>hain provides abstractions to make working with LLMs easy\",\n", "    #k=2 #hyperparameter,\n", "    filter={\"source\":\"news\"}\n", "    \n", ")"]}, {"cell_type": "code", "execution_count": 46, "id": "********", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'source': 'news'}"]}, "execution_count": 46, "metadata": {}, "output_type": "execute_result"}], "source": ["result[0].metadata"]}, {"cell_type": "code", "execution_count": 47, "id": "c82f3171", "metadata": {}, "outputs": [{"data": {"text/plain": ["'Robbers broke into the city bank and stole $1 million in cash.'"]}, "execution_count": 47, "metadata": {}, "output_type": "execute_result"}], "source": ["result[0].page_content"]}, {"cell_type": "code", "execution_count": 48, "id": "2d671e12", "metadata": {}, "outputs": [], "source": ["retriever=vector_store.as_retriever(search_kwargs={\"k\": 3})"]}, {"cell_type": "code", "execution_count": 49, "id": "5effe0a7", "metadata": {}, "outputs": [{"data": {"text/plain": ["[Document(id='e6ade172-ce20-4179-861e-147cf6e1c3d5', metadata={'source': 'tweet'}, page_content='Building an exciting new project with Lang<PERSON>hain - come check it out!'),\n", " Document(id='a6e1aa11-4850-438c-acef-78be27abb03a', metadata={'source': 'tweet'}, page_content='LangGraph is the best framework for building stateful, agentic applications!'),\n", " Document(id='85f4a46c-d5f6-4154-9685-1fdc071bf6cb', metadata={'source': 'tweet'}, page_content='I have a bad feeling I am going to get deleted :(')]"]}, "execution_count": 49, "metadata": {}, "output_type": "execute_result"}], "source": ["retriever.invoke(\"<PERSON><PERSON><PERSON><PERSON> provides abstractions to make working with LLMs easy\")"]}, {"cell_type": "code", "execution_count": null, "id": "b9b4a982", "metadata": {}, "outputs": [], "source": ["inmemory(server)\n", "ondisk(server)\n", "cloud(yet to discuss)"]}, {"cell_type": "code", "execution_count": 50, "id": "ac515e08", "metadata": {}, "outputs": [], "source": ["vector_store.save_local(\"today's class faiss index\")"]}, {"cell_type": "code", "execution_count": 53, "id": "1be9960c", "metadata": {}, "outputs": [], "source": ["new_vector_store=FAISS.load_local(\n", "  \"today's class faiss index\",embeddings ,allow_dangerous_deserialization=True\n", ")"]}, {"cell_type": "code", "execution_count": 55, "id": "11a998d9", "metadata": {}, "outputs": [{"data": {"text/plain": ["[Document(id='e6ade172-ce20-4179-861e-147cf6e1c3d5', metadata={'source': 'tweet'}, page_content='Building an exciting new project with Lang<PERSON>hain - come check it out!'),\n", " Document(id='a6e1aa11-4850-438c-acef-78be27abb03a', metadata={'source': 'tweet'}, page_content='LangGraph is the best framework for building stateful, agentic applications!'),\n", " Document(id='b4c2983a-bcf2-4ba0-9182-4e68b530a9aa', metadata={'source': 'tweet'}, page_content=\"Wow! That was an amazing movie. I can't wait to see it again.\"),\n", " Document(id='f3fbd2f5-dd62-4bfc-b03f-02863a8bcc73', metadata={'source': 'website'}, page_content='The top 10 soccer players in the world right now.')]"]}, "execution_count": 55, "metadata": {}, "output_type": "execute_result"}], "source": ["new_vector_store.similarity_search(\"langchain\")"]}, {"cell_type": "code", "execution_count": 56, "id": "0c1b2efc", "metadata": {}, "outputs": [], "source": ["from langchain_community.document_loaders import PyPDFLoader"]}, {"cell_type": "code", "execution_count": 58, "id": "ad023ba9", "metadata": {}, "outputs": [], "source": ["FILE_PATH=r\"C:\\Users\\<USER>\\agenticai-2.0\\data\\llama2.pdf\""]}, {"cell_type": "code", "execution_count": 60, "id": "0d4a26b3", "metadata": {}, "outputs": [], "source": ["loader=PyPDFLoader(FILE_PATH)"]}, {"cell_type": "code", "execution_count": 64, "id": "dfb939f6", "metadata": {}, "outputs": [{"data": {"text/plain": ["77"]}, "execution_count": 64, "metadata": {}, "output_type": "execute_result"}], "source": ["len(loader.load())"]}, {"cell_type": "code", "execution_count": 67, "id": "6d66a576", "metadata": {}, "outputs": [], "source": ["pages=loader.load()"]}, {"cell_type": "code", "execution_count": 68, "id": "6bfd9d3a", "metadata": {}, "outputs": [], "source": ["pages = []\n", "async for page in loader.alazy_load():\n", "    pages.append(page)"]}, {"cell_type": "code", "execution_count": 65, "id": "5875b246", "metadata": {}, "outputs": [], "source": ["from langchain_text_splitters import RecursiveCharacterTextSplitter"]}, {"cell_type": "code", "execution_count": 69, "id": "003d7d75", "metadata": {}, "outputs": [], "source": ["splitter = RecursiveCharacterTextSplitter(\n", "    chunk_size=500,#hyperparameter\n", "    chunk_overlap=50 #hyperparemeter\n", ")"]}, {"cell_type": "code", "execution_count": 70, "id": "11efb7b8", "metadata": {}, "outputs": [], "source": ["split_docs = splitter.split_documents(pages)"]}, {"cell_type": "code", "execution_count": 71, "id": "b4d8c703", "metadata": {}, "outputs": [{"data": {"text/plain": ["615"]}, "execution_count": 71, "metadata": {}, "output_type": "execute_result"}], "source": ["len(split_docs)"]}, {"cell_type": "code", "execution_count": 72, "id": "d6b572aa", "metadata": {}, "outputs": [], "source": ["index=faiss.IndexFlatIP(384)\n", "vector_store=FAISS(\n", "    embedding_function=embeddings,\n", "    index=index,\n", "    docstore=InMemoryDocstore(),\n", "    index_to_docstore_id={},\n", ")"]}, {"cell_type": "code", "execution_count": 73, "id": "ffd4525d", "metadata": {}, "outputs": [{"data": {"text/plain": ["['f025ac02-789e-4d7b-97c7-fa65b85352af',\n", " '46ea0e04-3cf2-4459-8dc6-89400d2c57d4',\n", " 'e2b243d1-d093-4d4b-ad80-fa298891afb2',\n", " '341c5edd-fcdd-4e6c-8857-306d6f15a49e',\n", " 'c000e1d7-872c-4652-aa83-182b4c105654',\n", " '708cefbf-2553-434f-b34f-7e907591ed68',\n", " '0df5e784-5312-4a75-9229-815d697288d8',\n", " '1ee8ab45-7b5a-44f6-a32d-f9f358d9aa6e',\n", " '22f5b5c9-723e-4813-83ec-84a3c6216ef2',\n", " 'd0993f5c-e5d7-4b13-81be-35acbd90c0b2',\n", " '90c6ea44-68c1-4224-a8f5-d4f05c361b66',\n", " '9c883d10-6727-4354-af9a-582651e6b4a3',\n", " '34b92259-4597-40b9-99ee-b0f6205982fb',\n", " '8d2f4798-8d61-4bd8-913f-29e2c30a3043',\n", " '4c12a2e8-4e6a-4bf3-ad17-a282b2f66c54',\n", " '8764331a-f2bd-40f6-9c78-bac28bd57d71',\n", " 'ee446a49-be69-4c4c-83a5-a7e4a5eb630b',\n", " '44b45c1f-3257-43ac-b0cb-8f9dd65dbc56',\n", " '79e762d3-40a1-4a91-a178-858cdcb8f783',\n", " 'd572e2cc-f3eb-4cb0-b9a2-5b42fbe21e16',\n", " 'c3f61d69-89cb-42a0-90f6-562bf85fe4e4',\n", " '61cabf5c-e7df-492c-80be-e6b862b1a00f',\n", " 'e5c90ad1-b983-489e-98da-8c317100c487',\n", " '413740f3-8cac-4c83-bd6b-bb0fd4b74912',\n", " '978f81cd-4673-4b91-b8f2-a60d4de1550e',\n", " '458b5069-5284-4a1a-983c-c3bfcb158763',\n", " 'a30c58de-50ae-4ac4-9d38-1f1c6361338d',\n", " '31c64f67-98b3-4e44-9a19-c09e9587aa5a',\n", " 'b7fa3591-d37b-412e-9e95-ba042f475c47',\n", " '5b69bc03-115d-4fa8-b792-cb1df8b59809',\n", " 'afc56f21-a358-4e7b-ae8e-3566a5fd8711',\n", " '2f956354-aa2b-4f05-948d-e3bcc842af0f',\n", " '56c9403c-504b-4bbc-a5b2-26c734492fab',\n", " '4626cbcc-bc42-4507-a51e-882d88266f2a',\n", " '22976946-4d0e-4513-ba6e-061404222cbe',\n", " 'c83f5139-a28e-4403-8bb3-445dbf936f3c',\n", " '53710cbd-69a6-4925-adbe-15e7e9bdd92d',\n", " '732f12e0-92ff-47ae-8354-539ecdae5d00',\n", " 'e44c8158-b7ff-460c-8e6f-b698b78f3a41',\n", " '0c28a599-ef73-4867-af11-42c8ac568959',\n", " '9df2757a-825d-4279-b150-b63974036110',\n", " '8a171694-e750-4d46-b634-e55e13686108',\n", " '806fee42-e91c-4cfd-bb33-a37d88fdcbf0',\n", " '2a9ba12e-2a6a-467c-b391-5b0b2865b5a1',\n", " '38226774-92f8-4d1f-8ba0-d304f6540263',\n", " '7b72c91e-b79f-4a9e-9ff8-49792ed68765',\n", " 'b7a6417c-4319-499a-8d23-28bb916a8c24',\n", " 'a13d83b1-78d5-4317-8d22-1336485a7b5d',\n", " 'cf4935bb-fc35-4279-97ee-e1a3b020ca89',\n", " '5b5fea86-b799-472d-9031-00d7c2c48bc0',\n", " '8c87e975-37d7-4d27-8c95-6665680b904c',\n", " 'c36e3d8f-b357-4b75-a54d-557f3ad5dc64',\n", " 'f03d318f-15cf-43a0-bf3f-82268d890d9e',\n", " '9366b496-10f8-4447-8c8c-f441eff5fe48',\n", " '1392d18e-5cbf-47c9-876d-fdaa2137354b',\n", " 'e6bbe689-4917-4339-8300-bc7c62988ffe',\n", " '3d8eab4f-f35a-4f6d-9ded-873df151ae0c',\n", " 'b329f8f2-cd41-4952-ae38-e1c1bfe464dc',\n", " '48001abf-abc1-4a3b-843f-a2f7115a3a30',\n", " '1645bfdd-56f6-4a16-a970-de2a62ca2c5b',\n", " '5575d160-5f8c-4728-84f7-00e3ec9b0d96',\n", " '073f3178-2399-4769-a1fe-708edfb56780',\n", " 'a698f9c4-3ccb-4947-a625-506a29f59baf',\n", " 'c820eb56-3dfb-4a6e-b23a-74790fb40985',\n", " '30ceb807-ff71-42f0-b725-3d171ff0d84b',\n", " '3127e74a-ddb6-4d6d-839f-95ad0e490937',\n", " '225ea758-ab71-4e49-b95b-73a2959b7e68',\n", " '8a9f2c9b-08c8-4a42-8637-561d4f677ff4',\n", " '617efa73-5e78-4b25-9987-20cb671ffea7',\n", " 'ff208a20-2d2a-41b6-8a18-48f8da577af4',\n", " '27e49497-8213-4ba5-bacc-591e800e75a5',\n", " '19f68d2d-d5ef-4f02-8c3e-3b11130b15cb',\n", " '38516712-42bc-4310-8a57-551342945fdc',\n", " 'bbcfb62a-72d3-4136-bd46-dc62e4c4c63b',\n", " 'a9293e80-5b05-421a-bb12-239de51280e3',\n", " '1035dc3b-1737-4cb5-8e89-386a6655c385',\n", " '6e1bc632-b2e5-4f7b-a018-9c9de048ee11',\n", " 'a2c80dd5-1d83-4896-a01c-22276cd06684',\n", " '7ce85f14-9f90-4224-bd83-afc128b68d0e',\n", " 'adc79d77-3c91-42c8-94b2-4b7abea4464d',\n", " '10a94484-bcf1-4954-a436-48abd2ef5c6e',\n", " '8cb0c5e0-40f8-4843-b4ee-126d8c534210',\n", " 'f76e9e22-04e7-4066-adf9-c8809e568210',\n", " 'fc97b32a-1801-4547-8526-3dc2d2491b87',\n", " '9457b58b-559d-44b2-80cf-e7715ba4294b',\n", " 'cf8874a9-5a71-45b3-8df4-47c3b59ab17c',\n", " 'a5a7a355-8575-4a70-aed5-3afd281a542a',\n", " 'ec2ec5ad-503e-4c3d-be2a-bfc4f71ce728',\n", " '7fc2ba1a-048b-4bdb-ba7c-0a7ad92aeaf3',\n", " '4563875e-973d-416f-943a-428ec7c961cc',\n", " '849a29fa-8862-47ba-a33c-50affa4196b8',\n", " '844dd297-6481-4cbe-be4f-a4e70a69414f',\n", " '3126e280-c39f-459d-acf0-f3c8c4e82fbe',\n", " '424f0fd1-90f6-494a-940b-73fa199a4813',\n", " 'd357feaa-d00b-46ca-91b3-f1cb8f8cdca2',\n", " 'b05b5ee7-f449-4627-b7cf-fd28bccb699e',\n", " '56b404ac-de37-4eb2-977e-edeb820b319d',\n", " '270b038b-4ee3-4789-9f48-179412ae2855',\n", " '8b3ef167-09ca-4602-917d-f1489f85ccc2',\n", " '237dab13-73e6-4897-a10a-d0d2efe3e540',\n", " '3f22785c-e455-4ae6-a4b3-828f41c092e9',\n", " '6607334c-0588-435a-b534-baf57de99c20',\n", " '5aa0126d-dea3-4db8-9e55-b24f49a87574',\n", " '404c1347-f48b-47e3-aca3-ea1280e0eef8',\n", " '41a8cdf5-362f-48f8-8ac2-2c786b7db22f',\n", " '870ef155-cfb5-4dea-b230-a8b9b9cc7165',\n", " '7cce0e99-c15d-4392-b864-e2baa482dbc4',\n", " 'c7d42e1c-aa4e-4767-9daf-b0c152bf1113',\n", " 'd631817e-b692-4062-b311-06e8ac56d5a6',\n", " '68440d76-caa6-47d0-a7e9-ba9c944fff00',\n", " '43d05134-6e59-4ffe-a3e7-dc59144bd558',\n", " '84cdbf13-8807-47be-b90f-8452bcdf57ed',\n", " '361b0aa7-d3ad-4778-96ec-7116f2538e50',\n", " 'aa9473e4-f8ba-4a2a-bb64-f7fcb1cdc1c7',\n", " 'ac50805b-f9d4-40c2-963a-cc5d4a5a392b',\n", " '49cca038-a828-4fe7-914e-6ad7c48b1148',\n", " '811f5728-4362-40e2-9c8c-56a677603662',\n", " '5fe09f18-529c-4caf-b86e-0b3040886e13',\n", " 'e8a21bb5-0091-4a97-8548-850dee4425e2',\n", " 'dd454154-0154-4beb-b11c-0896e5fbdccd',\n", " '8ae71413-1b99-442a-9ab4-92b31f0998ca',\n", " '5ec69d59-3adf-4aff-8c8c-00fd5d54b1b7',\n", " '91849d46-1dbf-4b93-9b1c-ad8bf642be0c',\n", " '371d41b4-d477-4cc0-8ea7-e68fa549bae5',\n", " '8f9e48de-14aa-470d-9278-5ffe51ae364b',\n", " 'ae010db4-4f15-461a-a23e-d9ca38cb7446',\n", " '9549d4bd-8686-41be-9a89-5e98e5f3459a',\n", " '57f471dd-8d3d-4403-b9ac-d88d0604f9dd',\n", " 'e68b1abf-2771-460f-b332-79b126e77298',\n", " 'a049b904-5b28-4c1e-9c3c-9a2a3d55aed6',\n", " 'c6899b5b-5d8c-48e7-9495-59942ea8b5f6',\n", " 'ddb7d5e8-c54a-4b90-a4ad-7f1ac4e87edd',\n", " '02283a93-3e8e-4e61-adbf-84040e4a15d2',\n", " '3149374e-2692-4d65-95f0-1df8047865a1',\n", " '5f4461c2-58d2-4673-8829-6d48d9c364af',\n", " '11065c2e-644e-4c1b-a982-df502b7ba950',\n", " 'be8175d6-9a3d-4057-9e09-728656cedc73',\n", " '7b0ef076-c99f-42e4-a95c-249fd244e8b3',\n", " 'e81c8a39-d6a4-4193-aa69-1bfa12d0e97a',\n", " 'd8bb47d7-d618-4ff0-b34b-b9e6a57eb8dc',\n", " '05df34e5-afb9-42af-a5b4-7550682d2fed',\n", " 'e0a2dfde-4195-418c-ac54-c71af6db1243',\n", " '7e526454-7866-4a0d-bf60-c1ce5037c206',\n", " 'd18f8743-82ad-402d-8c3b-7802b6e4edc6',\n", " '303bdc98-fe2f-47ab-953f-2d2b78c2c39d',\n", " '3dc38d74-a226-4427-89de-590666614068',\n", " '526813c0-a0a0-48c6-8e76-2bfd4ec85402',\n", " '66cd3614-006c-4547-9e34-b50ba92e7774',\n", " '38aa98d0-32c9-47ff-99dd-c519ab3b43ed',\n", " 'a3a2eebd-cae7-4111-9480-536df017a9a4',\n", " '0f7b72ca-904c-4b5b-8b1c-d4feb01c1549',\n", " '39aac20b-1f24-45bc-a098-96761b1a924d',\n", " '76dc2892-12ff-4123-ad64-496d5052c895',\n", " 'a0565b59-130f-446c-a9e2-9a699b46435c',\n", " 'f0c95519-3038-4246-a57d-b69ff13390a9',\n", " 'a95a1668-f74d-4afa-86ca-ec2a3d8f6cb9',\n", " 'e299f154-545b-490d-ac9e-d77eef5724b1',\n", " '8c798d29-c3b7-4471-8e05-9fb6a91fce82',\n", " 'becc0608-0d14-4dee-bccf-b26194a2ef0c',\n", " '9d4e5154-a5c7-46c2-9116-c57adf19c735',\n", " 'bd42b81b-286a-473b-bc72-7cfc375de068',\n", " '7fa927af-1e8d-4346-80f8-16faaed3dd53',\n", " '114c286f-a2ea-4665-95ce-3f732040ef27',\n", " 'edf72da4-aa7a-4803-8940-cb3a06dcbe24',\n", " '4b726306-722e-4af4-ab2d-7fea2c03207f',\n", " 'bf07ee80-342f-4c4b-b9ed-de8cdef37ea2',\n", " '3f061fd6-b661-4e7b-bb4b-342d0f74b913',\n", " '247db243-7fc8-47a9-ac5f-bdad8b0c8e35',\n", " '007f8e2e-c789-4d32-9d01-958eeed1d0f6',\n", " 'e1d4eca5-4bb8-4b4d-8022-9071befb3a1d',\n", " 'cf1cd73f-8f1d-49ab-8924-49b0280c6be2',\n", " 'd2767c88-c99e-4473-acbd-32850b45fa13',\n", " '1afe470b-bf7a-4815-a28f-ef852c7514a1',\n", " '64b77a4b-6abe-4f2c-9a32-2aaac710107b',\n", " 'ca484cf3-1a8a-4c3c-8a4b-415dc9fb73ea',\n", " 'd5c9f577-8ec2-4efd-9b51-5e55bf2bdb33',\n", " '7a4f6007-a73a-4453-a1d7-a50e6028f086',\n", " 'f117ba82-a43a-410e-a5fc-7e69eaf24841',\n", " '4a877437-99a4-42f3-bb1e-d206746da2f7',\n", " '44278bf8-afbd-4c4d-9e56-76de549db08d',\n", " 'ba790d23-ba85-497f-9099-2ab16d4df21a',\n", " '65c11dc8-a40d-4ce8-b4b1-ab0674bc023d',\n", " 'f97a4813-323d-472d-9344-b3de34fc0201',\n", " '03adb230-99d0-4491-b8a4-eaa7f5b5b714',\n", " 'b11931e9-86cd-4cd8-87db-d3360af698e2',\n", " '63570601-2c27-4e11-96ff-5ba9e8282bb4',\n", " '15e04293-3e5a-43e3-931b-cc42ebd74952',\n", " '8ed06145-b491-49a6-97d7-3bc1746fa06d',\n", " 'a81f9383-338a-441a-9dd4-fc83b06dfe20',\n", " '9d280216-f552-4ff6-89a4-e30c1e49bc22',\n", " 'fbd16d3f-be85-4186-8c17-7bfedcd6676f',\n", " '4f083e15-5017-4608-a711-6f2e2379f3f5',\n", " 'fdeb5cf3-46d1-4115-b6c5-cd0d51832377',\n", " 'afac458a-8370-461e-b081-7973b2cc6237',\n", " '5ad25226-501a-4cd8-8136-2784d8aa01e7',\n", " 'ff86b17d-34a9-4b67-9abc-a3fb241e31f8',\n", " '639c2270-84cc-40b7-86c9-28780a7fa20a',\n", " 'f86c7b90-5e11-45d8-b3e4-9aa8e519dcff',\n", " 'ef0c610d-5bb8-4c2d-928c-e6f0182525b2',\n", " '4d3ffbf2-4dbb-4de8-9753-8a687961ab84',\n", " 'd34e5305-8113-48e2-9299-0389c9914808',\n", " '6caa15ca-7ea2-46b8-85c3-ac53aa71300f',\n", " '5ce27b02-98ba-461a-8a39-f50f3950f657',\n", " '58a69653-abc3-4bd0-a073-ae700de2137c',\n", " '43dbffad-42a6-41e0-a0fe-d587f0989a15',\n", " 'b5112d20-f9c0-4031-b038-062b7d3477be',\n", " '275dd680-3974-4c54-972b-5999ae48e816',\n", " 'ba755d03-997c-4978-a850-7f749629f80e',\n", " 'b0bd1bc1-87f8-4a16-a736-9f161ee7ec80',\n", " '508c2c62-2a6e-44fe-9a08-c8918dba42ef',\n", " 'b3aff9be-726d-4f18-9cf8-2faf7f81129c',\n", " 'f9644e39-85d8-472d-a1ac-8c266a145b4e',\n", " '464f5fc1-4c55-4139-a4c6-ea36adfdae96',\n", " 'c4da6d6c-9c2e-42bd-91bb-e50944b835ec',\n", " '6910c72e-7809-427d-a4cc-a6394828379f',\n", " '9906d19e-d6ff-4ed1-bf68-59c347386752',\n", " '468e0d29-dc81-4d22-8d5a-11dc077d88b1',\n", " '786d3112-b07f-40f4-8ae9-7c3c7ce5d814',\n", " '46881fff-ed25-4509-9fd9-d4bf80e14964',\n", " 'ef05d7ac-8723-4b28-9ccb-6f717c57a313',\n", " 'fc196010-2c57-4be2-8eef-114bf04b09d7',\n", " '26ffe294-d111-4b46-9b18-527cc0f69fe0',\n", " '6d12c238-73c6-4f5f-838c-7169435eadcb',\n", " 'd67387e9-7f83-471e-b8ed-436c8960cf1d',\n", " '1f8eb7b7-2839-42c0-9604-a4574d3f693e',\n", " '756c1413-5638-4e2f-a9e1-7918aa16d6cc',\n", " '52ac9608-4bcf-44b2-bd1c-bb3ecf842ee9',\n", " '32e1bb11-b182-454c-b707-bd8f15a7f78f',\n", " 'a9bad78c-cf31-41bb-975a-fd747f4ccc66',\n", " '693009c4-ac1d-4a86-94a8-4938df84a42f',\n", " '974f4898-9b56-4772-9f8a-da3592197ecb',\n", " '31e1ed47-14fb-4c88-b767-a18034d74c7d',\n", " '091826c3-d2a4-48a2-ab0b-0089402a6fc8',\n", " 'e566bc11-a68b-43bc-890c-dd30d46c3c37',\n", " '824112fa-9c5c-43ab-9e9f-5a179547a0a8',\n", " '6d87db24-e508-4b59-9fd4-257cc2cb8c08',\n", " '35f4b298-8ed4-4099-8360-cada0d451706',\n", " '55abe78b-bac8-4a34-bfa5-c1b934<PERSON><PERSON><PERSON>',\n", " '774c6ae2-8e8a-4a24-8568-72039dbb0b30',\n", " '7f44e54f-7c35-4387-afe4-fdc9306f6cf7',\n", " '1d982634-b7da-40df-b36b-1d05872439b1',\n", " 'a82f8bce-da4e-42ff-8146-590ccfb3ef86',\n", " '00746426-b07d-4103-9393-b5f3b47d99da',\n", " 'e0b4a317-0e43-4409-a696-ed2c69247bff',\n", " '4c6a6f61-aa0c-4cba-8e4b-1d4cab0c87a7',\n", " 'a5a66097-42a3-4981-a4b5-646cba54c41b',\n", " '50e57a1e-fd79-48ef-9af1-3cfbdc1b18be',\n", " '0c0ab1ad-c204-43a8-acd5-5471de589499',\n", " '88ae58c5-59e5-4a7d-9017-dea7881575fd',\n", " '54666af0-260f-43e5-9a9f-fe82cb666ebb',\n", " 'e805ad80-56e5-452e-a886-6c5ccf490a59',\n", " '072730ab-ebe3-4edd-b1c3-03d783a2a5b9',\n", " '0e14d5da-e1ec-45de-b2ec-88a5d12be767',\n", " '40f98400-310a-476c-a4ac-fd658f7c051e',\n", " 'e0c8441f-65e6-4d1e-ba1e-150c7009b771',\n", " 'c7c59f0e-08d3-4990-a8e3-7f7952a3d80c',\n", " '6a407f16-be34-4e92-8778-b6abfe5f2e43',\n", " 'f0257bc6-d0c4-46ce-bdde-ee1f7eff1d09',\n", " 'af4923a8-5adc-4140-b947-6e648b0e9b45',\n", " '1bf2c6c9-6de4-4aaf-9740-6c806e849892',\n", " '7ec713cd-1a04-4ed3-abc9-4d7dee0b5265',\n", " '78de3775-7fd5-4ea8-8c43-12b6f45aac1b',\n", " '5c65c022-473a-46a0-9375-66a6987e9dbc',\n", " '958f9076-f367-4a10-8051-3c8e42d65e66',\n", " 'e7ca5bea-b6e9-45fb-a4ef-09d941ee4fb9',\n", " 'b8990a1b-e70f-4a3d-ae70-5c30a94cdfe1',\n", " 'dc803add-7209-4778-90ee-2e94e20edf0d',\n", " '9b846df2-ee6b-4db1-bc8a-8a888151daec',\n", " '35292545-6906-467f-b235-71687e209ab4',\n", " 'be072c17-009a-4e31-8f0c-a44fe5b58944',\n", " 'f998149b-2d0e-4e16-b83d-cca605013138',\n", " 'd931f1fb-3003-4c0d-bc86-9795707ab813',\n", " '18898b1e-d59e-4fa0-96a4-18179b03eb81',\n", " '0e8c1b4c-7ad7-445f-ba23-907e09d6bfe3',\n", " 'afec3415-4128-423c-a4e6-da33dc5fb497',\n", " 'd7411839-191d-4737-9f1a-b756b4e01e8a',\n", " '422b2256-d905-49b6-9258-7ef60428908a',\n", " '5cac4796-030c-42c6-ab22-3bb2147972be',\n", " '408aabea-1a13-4d66-97ff-14db0c8c48a7',\n", " 'e47aced3-e05c-423a-83c3-89dda50cd4d0',\n", " '07f1ffdb-81e9-479c-8c87-327c3b8c23b8',\n", " '4581d349-b22a-4d9f-a1c9-bf8b32d8a074',\n", " 'bfe10a99-ec08-493b-bca5-0e47394c0a9e',\n", " '09abdbc3-1044-4b30-86f9-3980eef9b3e5',\n", " 'b6041f19-d693-48ad-9de4-b7caf09c6520',\n", " 'cb5bbfc5-c158-4a86-be17-61ed99df2271',\n", " '9cd1fc5d-c721-4391-b482-b717315124b3',\n", " '0b8022d2-0ed5-4c65-adc0-4e3208ee159e',\n", " '151e3637-5dad-475c-bd3e-0bf1d34beccf',\n", " 'ce3dd749-22c5-44a0-bdbd-fde57c398717',\n", " '9414eddc-9fbb-4bc5-bd52-403ebb1c03a5',\n", " '8e5fe0a8-bf45-46f3-9af4-965c6ce1bb72',\n", " '78ce15f9-aae0-43bb-a054-f47ca80ca0e6',\n", " '92fb338a-bca0-49e8-904b-9b313d6846de',\n", " 'b31b8646-a27a-42d1-bc18-777c947d1687',\n", " '4cddb6a0-ea2b-423b-9e73-05577eefc66a',\n", " '6c1b77e7-83f3-44b4-9be5-90977ef3869c',\n", " 'ea7e3784-ed3e-4845-9429-5dba6957c63b',\n", " 'c3552447-bd86-4d6e-afb5-58e1b4decd84',\n", " '8acd4c1c-717b-44bf-8d1d-1c8127e5cffb',\n", " '273af0e9-b901-49d8-990f-6a4d9a27678e',\n", " 'da659a0f-c172-4126-9b3d-9bf686609175',\n", " 'dcbda2e4-4982-412b-b8c2-372e393b3ac5',\n", " '23870427-0136-42da-9e34-a6db0f521843',\n", " '77127136-79e8-458b-a236-ee6dd403319f',\n", " '3c9a1b46-13f5-42ab-a6b9-e7ceaf9de9b6',\n", " '553a2941-7090-4fdc-822f-918c58b2e4a2',\n", " '9b7df562-cd5f-4312-a5c3-ca93f4122882',\n", " '3fa394b6-225b-4440-b9c0-b480c0c253cd',\n", " '3285e186-a231-4de6-a638-14508aeca365',\n", " '16aa21ad-09ac-423e-bc93-edcf3a8d4af2',\n", " '185079c7-37e4-4ab2-a97e-70e3206a0194',\n", " '3e805b95-5194-4809-9a2c-b10b991913cd',\n", " 'a40410e5-89d7-45a8-acff-836f9952e04f',\n", " 'af3cac65-be84-419f-bb56-18a350a38066',\n", " '61b3253c-a1d4-4ac8-82f3-10333cce7014',\n", " 'a187679b-4cb5-4a7e-aaea-1245b7223fa8',\n", " '6f414765-9c5d-49e3-8a80-b6ffd49436a1',\n", " 'f138272f-4282-4244-988b-2cad629b1019',\n", " 'c916f400-ad4c-460b-8472-745b133d8b8f',\n", " 'cbc70e2e-4e24-4618-8787-b1f706db510d',\n", " 'c509f125-f020-4feb-8aea-f0c1703c0ede',\n", " '8b386d45-d369-400e-a121-28f79766086e',\n", " '30797397-13cb-4efd-8b5a-ed195febb335',\n", " 'cefe1c4d-286f-4ab8-b047-e80e1d18a1b6',\n", " 'ce7f93e7-db81-4db9-a615-40bf8369dc37',\n", " 'ca2155bf-f696-4737-8231-d13a9ea5260b',\n", " 'b3fb9fe5-811a-4eb5-b87c-86171ec03aed',\n", " '6a014fb2-1238-4a17-8932-652647b0d40e',\n", " 'f6d21dd8-053f-4a26-94b5-1849e30831bb',\n", " '292e9dd4-1ca9-4135-a935-f4990c93292b',\n", " '60595746-5de4-4849-ae62-1beed96e85e0',\n", " '640d354a-1709-4716-a236-9c9ba76b5174',\n", " 'f2a98ee7-ea37-457e-9a0f-9c7af74d71c0',\n", " 'd2d892d2-cfc8-4cd9-8b17-4cd61ef39b2c',\n", " 'bb64584c-2106-42c0-8d55-603578d43987',\n", " '7394150f-4236-4d7c-8032-0505c4f602cd',\n", " '5b4dc986-8afd-4b38-93af-3ff6413524f5',\n", " '7b48b9b0-8de4-451d-8338-f91771638b17',\n", " 'e6946537-8208-4ddd-b49d-92a6fc8625dc',\n", " '01b54397-a1f4-4c2f-bc5c-6363ad13712c',\n", " '68b606c8-6468-42b6-98c8-7828aa71d4f9',\n", " 'b9eadbb7-5fe8-4bf3-98af-3255b3565268',\n", " '68131442-56b3-4d43-bd77-02e17bc165e6',\n", " '5b1c4458-f4eb-46d8-8ee8-e8be233975e0',\n", " 'f364e93b-d6e8-48c1-b4b4-37be96ec6805',\n", " 'ede08ce2-338c-499d-8ebf-bebbfa075cbd',\n", " '7fab5c00-69bc-4774-b8b8-e44f46240ad8',\n", " '8be33eb2-4739-4fb9-9860-ba4843fa2582',\n", " '6b1afa57-c974-4749-a447-7cbe2cd53aef',\n", " 'c8acd8a4-9237-4580-8c03-96f252ae9211',\n", " 'f28ecb6e-f37d-4ed9-9ed1-2db3270a10a2',\n", " '75b53ea2-b6fb-4b27-baeb-0c7098255695',\n", " 'e6c6b758-1f4d-45f7-9f51-15752689bf82',\n", " '9776bc43-b3e7-4c50-8bdb-16be385e3715',\n", " '2a505dc5-8ba3-4255-9707-29f2fee9de16',\n", " '1186f69c-a528-4563-b2e0-3f821debb2e0',\n", " '65656cf3-0cce-4eed-976e-f7693b142ee9',\n", " '250acd59-5b3d-41d2-aa35-e5046378234b',\n", " 'e838a120-4741-4e52-9ac1-b2761736ab3c',\n", " 'd83cac49-dc9b-4241-b7d4-a54240c0f5c7',\n", " '097fcafa-3d8d-46be-b42c-111849b0291d',\n", " '34de2a54-caa9-452a-932e-09bcb318518b',\n", " 'a773064d-c0bb-4ce2-abda-5a8c7affe1ac',\n", " '0eaa7390-3d34-4c39-be99-20662e192f27',\n", " '89693474-8fe4-4edd-b381-6c2cec1a2a87',\n", " '93f82207-c38d-40d3-9a69-c68a2550aad3',\n", " '08c9b55f-0f07-436e-b81f-2d58a3c05607',\n", " '1f159583-1246-4045-a5b3-926f9aa75d4c',\n", " 'd1133292-5a94-44ff-80bb-3c1fd98ef029',\n", " '0fc87d14-686a-4946-a6fe-262986747797',\n", " '30429e62-9612-49a5-93e3-a26c4c52f93f',\n", " '34c77abb-4e9c-479d-9ca1-e56f9686d452',\n", " '7dd127c5-0cce-4c86-b2e7-a97e2281893e',\n", " '32505930-d625-4c29-a5b7-5517287db883',\n", " 'fd62403f-7a69-45c5-a8ff-2d54cffa3f3c',\n", " 'b2653f84-cee7-4d35-804f-e3b55005dfca',\n", " '80a3dfad-fefa-4983-9fc5-8f90ecca62a3',\n", " '36997e9d-21fd-4291-a72b-b6672e2257d4',\n", " 'e31f3cd7-9b45-409d-b905-8ca30ef56369',\n", " '9ef41462-8d7f-4cb0-b7b6-d7ba9d09f335',\n", " 'a6d74bfc-9d60-4fa0-aadb-67946fda280a',\n", " '9633c21f-32bc-466e-b6cc-d731a753431c',\n", " 'ddd8c793-9282-499b-b3b1-095bde1d971f',\n", " '8cbf5739-9e94-4cfe-965b-b557cc0e709c',\n", " '7e0558d0-82d4-435f-ac26-5479cccaeb56',\n", " '35b70966-cfbc-4c3c-b654-034b6d4144af',\n", " '2dc3c1cf-fc19-459c-b7de-b0f6a29ddb4a',\n", " '700e482c-3773-4f5b-bca9-c26e85202022',\n", " '946c12bd-ca60-4d96-a214-d1d36add5b12',\n", " 'be886785-f259-4f73-bd03-73ff8289646e',\n", " '738f9ce5-d434-4654-81b9-15b6f3d6fe7a',\n", " '90755a1b-f1ea-468a-bbf4-d42e51de6c4c',\n", " '5a82f9aa-c444-440b-99b3-79a371183d98',\n", " 'b5bb5c8d-1829-44e1-ba10-de2e6c436cec',\n", " 'fd971205-18c9-4b33-b852-8d022ceb0d86',\n", " 'f3711899-b14b-46b8-b3de-24728a9f2015',\n", " '2ca5c2b3-88da-4b32-bc4b-a7be6cec303a',\n", " 'cdece2b7-2aa5-4b6d-be81-a78db16e3159',\n", " '96336026-605d-4b18-8f3f-b1784bd0d705',\n", " '04999faa-c67b-4e37-a4bb-d47e34a710f9',\n", " '671b6155-7d6f-49f6-9cb3-4e3f131e07e1',\n", " 'bbdfb502-e3f4-432f-93e5-fe83544ac3f4',\n", " 'caccd474-9a42-42e0-ac40-a3904bae077b',\n", " 'fb34e47e-ee59-4ea6-9527-66f8e25fd6c9',\n", " '8746a5c4-730d-4a58-8b88-fcbbf54bff39',\n", " '10742aa2-bfc1-482b-b09e-df8671859529',\n", " 'ddf415af-f4d4-4d8b-a4da-ddc425783925',\n", " '8741371d-96b1-4b24-91a7-3bbbf68c15ba',\n", " 'bef9bf9e-8995-40da-b6ca-361b5a979a7a',\n", " 'df6bc07a-a59d-4e8c-8e18-efd4b8e80b8a',\n", " '7d273712-eb75-463d-afe3-67edba862080',\n", " '4ff1159e-2a73-4834-a6a7-41b0fb435a99',\n", " 'e82ff9ea-fd14-4c56-990b-d2b4cbeedc21',\n", " '88b1cf14-66d7-4ccf-8aa2-1961f636dd07',\n", " '5edb580b-582c-417b-9cb7-58787f082dd5',\n", " 'e4987aa1-ba2a-4d9e-9fb1-bbd102ecf916',\n", " '95610b4f-9171-4412-a12f-168b1516d2ce',\n", " '12014785-94ea-4839-bacd-af21a4125ae5',\n", " '67eced10-4dc1-4971-a337-d2f638d38a01',\n", " '1265b893-fdee-4b8f-a0f7-15f3b979d294',\n", " 'a4327dba-d737-42d4-aad6-890dfd72238a',\n", " '2ef7e2d6-264e-481d-92f0-2774e12f6c40',\n", " 'aa8e0e78-ee5f-430c-a274-d6a72646c505',\n", " 'feb94228-65d9-415d-a1a2-80d7b8e8aff3',\n", " 'a66c9520-513f-40c7-8ed1-325b088fc42d',\n", " '3f347e2d-f820-4ca8-86d9-a421e77ea868',\n", " '7d2b38ea-9578-45c6-ab02-141de6bc8481',\n", " '0c71541a-ec3d-44f7-bab7-a5939a58d24d',\n", " 'cda26871-377c-45b9-9d1f-de6140fdc25e',\n", " 'db2e8d9b-ec0b-48a5-a0cf-4cfc447816f4',\n", " 'dba8506c-9b6a-4341-864c-f1adab0bbc9a',\n", " '343b9adb-fd04-4a2d-b4e0-78fc30c97f49',\n", " '2b67a0c9-bdb3-4f45-ab27-24cca28f0c48',\n", " 'c8f689d1-ffea-4fe3-a400-5fec773a790e',\n", " '0e50aa8a-36f8-4ec0-8791-b97511ee1f54',\n", " 'de61e7eb-b8f0-4ef4-a167-cb71e3c3c59c',\n", " 'ca9bee4e-649a-47df-8d65-f2fef3231a18',\n", " 'ffa6e030-965e-438a-a848-9104ee30adb9',\n", " 'ddde30c1-aae7-44fe-a3a1-da4d8f204f21',\n", " '085928b0-ddfa-4f7a-b274-d64f1d9fb9dc',\n", " '96b56e7f-ef5d-43f5-be9c-31770b795df0',\n", " '2c561f8e-1e51-4137-9f62-8d6d367ec53a',\n", " 'd083cfea-9abf-4ecc-a9c2-f14b6eb4a4ea',\n", " 'ae5d47a6-870c-4aa6-9e95-ec1bfd83e08e',\n", " 'b8d87ff1-7659-4240-9036-c53fd5ba9658',\n", " '672b4bf6-4712-43c8-b1a5-980b6aa4eda7',\n", " 'adc6061c-185b-497c-890f-bb6169369587',\n", " '8b463618-d22c-4262-a8b0-f48942b3addf',\n", " '1a321e26-fae7-4d9f-885a-a8c82afa78d8',\n", " '98c90d61-86e5-428c-bfc9-e12405a1ff26',\n", " '11715204-7c21-45bd-89c9-1562eb196be1',\n", " '15967077-28d8-4dae-ac52-b3e5e82b25a9',\n", " 'a91a97ef-e64a-44a5-b882-e2d5418baf13',\n", " 'e9c818a5-3658-437a-9371-eedee0c7dfc9',\n", " 'f7404289-e0f2-4a39-9207-5a3b4330b2c7',\n", " '3e466730-cd78-4899-b3ae-7f2cdafb35ca',\n", " '9c6cabce-2d6b-42a8-8747-d34715715859',\n", " '934473f5-3a6b-41e6-bc43-41e572d7b810',\n", " '7817d6ff-b2af-4fca-822b-f5d50e2e9927',\n", " '9b463b91-f821-4dd7-bcd8-edfa8d500545',\n", " '5ae759f7-6637-4ceb-a148-77f93042af0c',\n", " '5f2b8434-89cc-432e-8517-d101601d344f',\n", " 'c2b9dbc8-c4da-4b62-bdf2-5416b2c5ef59',\n", " '70540032-3b25-4869-8121-135581d663da',\n", " '7f5049a5-28b2-408f-a8b0-5fa851a3e26a',\n", " 'ba854912-7a84-490c-8cc2-594842b7b428',\n", " '1245a9d1-c383-4e83-a97f-7fc6f01f6d75',\n", " '953eebfd-81f7-4983-afaf-04cf9ce6b26a',\n", " 'a682cdef-cd6c-470c-8a80-e070c6c952ba',\n", " '0fa65fb2-130d-4338-b98d-7311ddc6f7a3',\n", " '9ec27937-0a6e-4c23-bf06-37a99a63afa8',\n", " '3e478718-02e7-4a1e-aa3a-1763fa4a2836',\n", " '0cc7c496-8c72-47e4-9035-9613d1623754',\n", " '0524ee31-bd93-4c7a-9075-8c484950e6f2',\n", " '904e648a-6cb1-45d0-a13e-5027e0f061c9',\n", " '765fddf7-f808-49c6-96ba-43f2f157a676',\n", " '8995d065-8c4a-4a8a-9047-5daeb330eb69',\n", " '03da48c1-c2a6-492f-87f4-9465e14a95b2',\n", " '180bb905-f5e2-4ae7-b060-3cdea9e20a1f',\n", " 'aa2df34a-90f5-410f-a972-91e0b175c3d3',\n", " 'aed93806-6745-4f3a-b6c4-4ac6871a652e',\n", " '169132ea-8df9-4799-b7fc-62703f85bf88',\n", " '1a80c188-3d6e-4b48-9605-8633f530c556',\n", " '1cf9b381-7733-4bc9-8a38-8f38e80664aa',\n", " 'd9bfc5cc-386e-4656-a5e2-54fdbe458f26',\n", " 'c2d24338-84a9-4475-a1bf-b3dc19e3498e',\n", " '1d71c664-dc51-4fde-b85c-45155f282e7f',\n", " '10d90bca-0f29-426e-b1fc-dda8f95c44ce',\n", " '278c7054-8650-4aca-a54b-a18dd9977218',\n", " 'ec50bf70-bde0-475e-a6bc-707f2c39d444',\n", " '1e710aea-12bc-4398-bf5d-cefad97c18a7',\n", " '5038a1f5-5998-4e58-8a9d-d283f9f939b9',\n", " '0c743c51-c82e-4c89-83f1-2d80f2e5889c',\n", " 'bda4aabf-cc27-4020-bd48-dbca553fef21',\n", " '765c2673-456e-4750-985f-84d5198613d2',\n", " '57663c84-986b-4bc6-9d5f-f825d3e98fb2',\n", " 'c1eeccca-634b-46a7-9af2-1bc3c6914202',\n", " '5e033356-7710-46cd-aaaf-54f5c59a580d',\n", " '2ce874ba-97ce-4e77-ad68-53d8564dbe4f',\n", " '874152bc-298d-4ae6-9a8a-978e338d6c2d',\n", " '0a8f765e-ea0b-4fd1-8ed7-2dbc3a9ba8cd',\n", " '8c14a3d6-8719-4953-85e4-c61427360d94',\n", " '2027579c-57da-4529-b728-ea079841290e',\n", " '7be1d658-8190-4b1b-b2c8-9d84d1e236d2',\n", " '4cae5f3f-a463-4efe-b192-e1e406e0e769',\n", " '07ffb81b-0f4e-40b9-909a-406cc59a5e72',\n", " '4a1c46b7-b58a-4af7-8a5d-99678de72466',\n", " 'c29747f0-a127-4fd0-8188-dc5845633fec',\n", " '9e814304-a26a-4f78-963c-515567b94c71',\n", " '45592339-0702-4653-9b5c-81b26c7877aa',\n", " '1ad5bc71-c000-4dd8-a1d3-03422859508d',\n", " '11b05603-1aa3-4618-965d-b39f40329914',\n", " '0c1b1ad4-c8c5-4fa4-826a-30ca67ecc2e7',\n", " 'c08d104c-0816-44b1-934f-036fc41340c5',\n", " '137926db-3f77-4dd5-9754-93a19119875a',\n", " '774141a5-290a-480c-81ea-794b68e7f9ec',\n", " '2549c1f5-231f-4160-9351-ba7b88567122',\n", " '190dc141-18e0-4859-87e8-d9ad11c00160',\n", " 'e8c49eee-0bb7-4067-a869-8ade2b12acb2',\n", " '5f9374b3-4854-4d09-840f-9226efef8959',\n", " '7a0220bc-7ff3-478f-8f9a-7d1ee65ac464',\n", " '3b6e95db-3efb-48c6-8972-897f8d507e2f',\n", " '18ba1c8c-dbbb-4f35-910a-84c5ab70ff81',\n", " 'f404feca-10bb-4bff-9571-24a685bb3edf',\n", " 'a844763e-a02e-49c3-9ba5-ccdfa80f45b0',\n", " 'ca075622-ca56-4a8f-92b2-acd7122b133e',\n", " '0907b62c-e42b-4d98-829f-1783a01ee547',\n", " '66fdcd96-e70c-4b8f-be24-a48d084ba245',\n", " '4d2b772e-50d7-40d6-9c0b-cff2030456c7',\n", " '0f5f92e7-f7c6-4904-ac59-ba73f4fee878',\n", " '7bdcee7c-c6ac-4908-814f-f01ef685a99f',\n", " '0f1f46f2-cee2-43ff-b2c0-79febcbfcba9',\n", " '969c222f-9e50-40fc-a8d2-2a19fdb2192e',\n", " 'adbbbc75-d5da-40cd-94d0-c777dd8f5d86',\n", " 'a8f4af86-76ec-46a6-b0dc-8ad00a0169b4',\n", " '008105d4-a13e-4cdd-a4d7-10f019ece39c',\n", " '2c4e0a43-4cc7-4f40-a9f9-2633aea2edbe',\n", " 'df7db1ac-a2d4-496e-8459-c4e1950e0afa',\n", " 'ffac3ea4-7f9c-4c7f-b2e8-4874796bab6b',\n", " '6e3ce2d1-f817-4a7f-a3b8-a086015576e5',\n", " '704c0eba-d195-4567-9e55-543c5f1967a4',\n", " '86cc2c14-3434-4eaf-9450-6049ce77cc18',\n", " '79992f80-788a-4568-9885-c37d22a640dd',\n", " '0e59e499-4d30-443c-b153-be96e7f28295',\n", " 'd912a9fc-a8ff-41f0-a8bc-03b5f4e2a545',\n", " '5b7a4648-e7dd-40a0-8cb4-c46c0a73db97',\n", " 'aed83c07-d49e-4fb8-8579-bfe02373a9fb',\n", " '0ed7b9b4-51ac-45b6-bd1f-91a927dbfb2e',\n", " 'ec8aff04-254c-490f-b450-afbecc5e8179',\n", " '6429bf58-dfbd-4394-ac3d-f0eda47eabe2',\n", " 'e1118f3f-ffa8-45a6-adaa-609e2695822b',\n", " '1887e544-449a-42d8-a1b7-faccdc0b9f74',\n", " 'ad99bae0-59ee-474c-b166-ed515112834f',\n", " 'd7348cd3-03ed-44fd-ba32-302ec2446c16',\n", " '25daf040-5718-4d08-a6fa-cca826f64aa1',\n", " '7c76287b-8a23-45bb-8032-af52ade81587',\n", " '565c362c-f329-4537-98ce-8244b658b6ea',\n", " 'fe2685dd-f136-46a2-b9d0-c3b5ba579800',\n", " '7debd417-a192-4b62-ad1f-9139e938d73d',\n", " 'b8fb9fe5-3169-41e8-986a-1022e652b719',\n", " '4e876d8b-b846-40dd-b380-684cdcd6016e',\n", " '909d6cb3-254d-4691-b03d-f2fe62e12ace',\n", " 'c919233d-83bd-43c1-9dbd-cd69d59beb5a',\n", " 'b7785ec9-f699-46b1-ba63-600f4d3e1628',\n", " '061d3b7b-bbf0-4676-bbe0-521c16b408a7',\n", " 'd381ce92-f6e8-49bd-9e8d-aa4576c1c048',\n", " 'dfd6fa32-9db1-43f1-8640-c90b6893f94e',\n", " 'ace4ca68-e925-48ee-ae9e-a11e34682a74',\n", " 'af544dae-753f-4182-b3a0-44902a80110c',\n", " 'a2feb902-2564-42e6-a999-bbeefc5dc314',\n", " '5d9c0670-4387-48c3-b8f7-0db58c0c1c29',\n", " 'c1fc73c3-e799-4972-a804-2fcba8299e8d',\n", " '8a46df88-2cd9-48f2-9f80-deecec94ccba',\n", " 'e6ff0bdf-c87b-4bbf-976d-30e347e9c03c',\n", " 'f9990b73-65ea-4f32-bd20-0699acddd362',\n", " '9b53d594-25b1-437e-bff2-bce9c42bde73',\n", " '62cbc406-b75d-4f91-b184-ff9a7b3b7d4f',\n", " 'c5c9da22-3993-49e3-9e1b-3916fc7fe83a',\n", " '7d7539ac-8569-4dae-b65a-19e7bb9d2669',\n", " '036b843c-4f87-472b-802f-1dbc4b339154',\n", " 'c435752d-d4ad-4d09-b8c4-913f6e35c93b',\n", " '32094753-9653-4d61-aa5a-afc5ca6e6447',\n", " '456bcf60-2961-41f9-aaea-d23ff2531297',\n", " 'd11225b0-d18c-4ed4-b7e6-08250e6f8df6',\n", " 'ee7da898-a0d2-4f33-a3ef-96ed4bb995c1',\n", " '1364854b-97cc-4744-8adc-2fdb349a7db7',\n", " '465c097a-266b-4888-b651-f71434e91ab2',\n", " '4c97657a-1bea-488a-8837-e37a7e374f52',\n", " 'b2a820a4-7153-47f7-bc95-ae9055dd4ced',\n", " 'ec106cf4-c0ec-497a-8a78-75675a1bc3da',\n", " '417e6d07-5e6e-4849-b12c-37ad9b4c8e58',\n", " '450bd499-c946-454a-9e61-1028a6f238c1',\n", " '791ca3fd-e7a9-49c8-8c3a-6daf172c20c4',\n", " '6b426e0e-2524-41c8-923e-e01c76ec1940',\n", " '0c1fd85a-f939-431b-bd4c-fc372c9596b6',\n", " 'd0e9f978-5fb1-4697-bfbd-56fd7b6e61fc',\n", " '20273aab-5292-4d84-97f1-0e89d06d9e21',\n", " 'cff75977-f0b6-4a54-a119-53f0929713e8',\n", " 'b7c91fb2-73df-4505-a6e9-95cf120bcb4f',\n", " '62ddb9f7-479d-465d-98e4-f2283f66eeb3',\n", " '9cb48d5c-b809-40b2-9eb5-2f40aecc5c6d',\n", " '343d648c-ed92-4b98-86aa-c63c36860a10',\n", " '817d74ae-e440-464d-a003-f9f92e501d5a',\n", " 'd3415cd6-81e3-4d03-ae4d-f56b6ce44970',\n", " '0d79a341-bbb2-4587-a5a0-20163731fc3a',\n", " '2882fa3c-de9a-4299-8cf0-763c5ce14587',\n", " 'da397b73-40ac-49c1-a705-49bf25986460',\n", " '5ba358d5-6e96-4fd7-a295-e864fc3f3d5e',\n", " '906cbd60-0c00-46cf-a5b3-f5df958ed4cc',\n", " 'b1a7dfd9-5050-4761-b626-68a8b958c109',\n", " '1d96a314-5a05-4e6c-a216-7685f7da9c83',\n", " '7e4ebe0c-817d-4007-928a-548379d963b3',\n", " '2a350012-aa07-453a-b6ab-fb38a22b7bbe',\n", " '97168931-26df-47cc-8a21-7122badd3296']"]}, "execution_count": 73, "metadata": {}, "output_type": "execute_result"}], "source": ["vector_store.add_documents(documents=split_docs)"]}, {"cell_type": "code", "execution_count": 78, "id": "95c466cc", "metadata": {}, "outputs": [], "source": ["retriever=vector_store.as_retriever(\n", "    search_kwargs={\"k\": 10} #hyperparameter\n", ")"]}, {"cell_type": "code", "execution_count": 79, "id": "39330aee", "metadata": {}, "outputs": [{"data": {"text/plain": ["[Document(id='978f81cd-4673-4b91-b8f2-a60d4de1550e', metadata={'producer': 'pdfTeX-1.40.25', 'creator': 'LaTeX with hyperref', 'creationdate': '2023-07-20T00:30:36+00:00', 'author': '', 'keywords': '', 'moddate': '2023-07-20T00:30:36+00:00', 'ptex.fullbanner': 'This is pdfTeX, Version 3.141592653-2.6-1.40.25 (TeX Live 2023) kpathsea version 6.3.5', 'subject': '', 'title': '', 'trapped': '/False', 'source': 'C:\\\\Users\\\\<USER>\\\\agenticai-2.0\\\\data\\\\llama2.pdf', 'total_pages': 77, 'page': 3, 'page_label': '4'}, page_content='work (Section 6), and conclusions (Section 7).\\n‡https://ai.meta.com/resources/models-and-libraries/llama/\\n§We are delaying the release of the 34B model due to a lack of time to sufficiently red team.\\n¶https://ai.meta.com/llama\\n‖https://github.com/facebookresearch/llama\\n4'),\n", " Document(id='89693474-8fe4-4edd-b381-6c2cec1a2a87', metadata={'producer': 'pdfTeX-1.40.25', 'creator': 'LaTeX with hyperref', 'creationdate': '2023-07-20T00:30:36+00:00', 'author': '', 'keywords': '', 'moddate': '2023-07-20T00:30:36+00:00', 'ptex.fullbanner': 'This is pdfTeX, Version 3.141592653-2.6-1.40.25 (TeX Live 2023) kpathsea version 6.3.5', 'subject': '', 'title': '', 'trapped': '/False', 'source': 'C:\\\\Users\\\\<USER>\\\\agenticai-2.0\\\\data\\\\llama2.pdf', 'total_pages': 77, 'page': 42, 'page_label': '43'}, page_content='10.18653/v1/2022.gebnlp-1.13. URLhttps://aclanthology.org/2022.gebnlp-1.13.\\<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>,and<PERSON><PERSON>than<PERSON><PERSON><PERSON>.Commonsenseqa: Aquestionanswering\\nchallenge targeting commonsense knowledge.arXiv preprint arXiv:1811.00937, 2018.\\n<PERSON><PERSON><PERSON> <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, T<PERSON>yi <PERSON>, <PERSON>n <PERSON>ois, <PERSON><PERSON><PERSON> <PERSON>, <PERSON> <PERSON>rin, <PERSON> <PERSON>, and\\nTats<PERSON><PERSON> <PERSON>. <PERSON><PERSON><PERSON>. <PERSON> alpa<PERSON>: <PERSON> instruction-following llama model.https://github.com/\\ntatsu-lab/stan<PERSON>_alpa<PERSON>, 2023.'),\n", " Document(id='906cbd60-0c00-46cf-a5b3-f5df958ed4cc', metadata={'producer': 'pdfTeX-1.40.25', 'creator': 'LaTeX with hyperref', 'creationdate': '2023-07-20T00:30:36+00:00', 'author': '', 'keywords': '', 'moddate': '2023-07-20T00:30:36+00:00', 'ptex.fullbanner': 'This is pdfTeX, Version 3.141592653-2.6-1.40.25 (TeX Live 2023) kpathsea version 6.3.5', 'subject': '', 'title': '', 'trapped': '/False', 'source': 'C:\\\\Users\\\\<USER>\\\\agenticai-2.0\\\\data\\\\llama2.pdf', 'total_pages': 77, 'page': 76, 'page_label': '77'}, page_content='models-and-libraries/llama-downloads/\\nWhere to send com-\\nments\\nInstructions on how to provide feedback or comments on the model can be\\nfound in the model README, or by opening an issue in the GitHub repository\\n(https://github.com/facebookresearch/llama/).\\nIntended Use\\nIntended Use CasesLlama 2is intended for commercial and research use in English. Tuned models\\nare intended for assistant-like chat, whereas pretrained models can be adapted\\nfor a variety of natural language generation tasks.'),\n", " Document(id='35b70966-cfbc-4c3c-b654-034b6d4144af', metadata={'producer': 'pdfTeX-1.40.25', 'creator': 'LaTeX with hyperref', 'creationdate': '2023-07-20T00:30:36+00:00', 'author': '', 'keywords': '', 'moddate': '2023-07-20T00:30:36+00:00', 'ptex.fullbanner': 'This is pdfTeX, Version 3.141592653-2.6-1.40.25 (TeX Live 2023) kpathsea version 6.3.5', 'subject': '', 'title': '', 'trapped': '/False', 'source': 'C:\\\\Users\\\\<USER>\\\\agenticai-2.0\\\\data\\\\llama2.pdf', 'total_pages': 77, 'page': 46, 'page_label': '47'}, page_content='• <PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, members of the original\\nLlama team who helped get this work started.\\n• <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>, who gave us some design input on the figures in the\\npaper.\\n• <PERSON><PERSON><PERSON> for the discussions about RLHF that inspired our Figure 20, and his contribution to the\\ninternal demo.\\n• Earlyreviewersofthispaper,whohelpedusimproveitsquality,includingMikeLewis,JoellePineau,'),\n", " Document(id='cf4935bb-fc35-4279-97ee-e1a3b020ca89', metadata={'producer': 'pdfTeX-1.40.25', 'creator': 'LaTeX with hyperref', 'creationdate': '2023-07-20T00:30:36+00:00', 'author': '', 'keywords': '', 'moddate': '2023-07-20T00:30:36+00:00', 'ptex.fullbanner': 'This is pdfTeX, Version 3.141592653-2.6-1.40.25 (TeX Live 2023) kpathsea version 6.3.5', 'subject': '', 'title': '', 'trapped': '/False', 'source': 'C:\\\\Users\\\\<USER>\\\\agenticai-2.0\\\\data\\\\llama2.pdf', 'total_pages': 77, 'page': 7, 'page_label': '8'}, page_content='et al., 2023). For AGI Eval, we only evaluate on the English tasks and report the average.\\nAs shown in Table 3,Llama 2models outperformLlama 1models. In particular,Llama 270B improves the\\nresultsonMMLUandBBHby ≈5and ≈8points, respectively, comparedtoLlama 165B.Llama 27Band30B\\nmodelsoutperformMPTmodelsofthecorrespondingsizeonallcategoriesbesidescodebenchmarks. Forthe\\nFalcon models,Llama 27B and 34B outperform Falcon 7B and 40B models on all categories of benchmarks.'),\n", " Document(id='ee446a49-be69-4c4c-83a5-a7e4a5eb630b', metadata={'producer': 'pdfTeX-1.40.25', 'creator': 'LaTeX with hyperref', 'creationdate': '2023-07-20T00:30:36+00:00', 'author': '', 'keywords': '', 'moddate': '2023-07-20T00:30:36+00:00', 'ptex.fullbanner': 'This is pdfTeX, Version 3.141592653-2.6-1.40.25 (TeX Live 2023) kpathsea version 6.3.5', 'subject': '', 'title': '', 'trapped': '/False', 'source': 'C:\\\\Users\\\\<USER>\\\\agenticai-2.0\\\\data\\\\llama2.pdf', 'total_pages': 77, 'page': 2, 'page_label': '3'}, page_content='Llama 2-Chat, at scales up to 70B parameters. On the series of helpfulness and safety benchmarks we tested,\\nLlama 2-Chatmodels generally perform better than existing open-source models. They also appear to\\nbe on par with some of the closed-source models, at least on the human evaluations we performed (see\\nFigures 1 and 3). We have taken measures to increase the safety of these models, using safety-specific data'),\n", " Document(id='5ba358d5-6e96-4fd7-a295-e864fc3f3d5e', metadata={'producer': 'pdfTeX-1.40.25', 'creator': 'LaTeX with hyperref', 'creationdate': '2023-07-20T00:30:36+00:00', 'author': '', 'keywords': '', 'moddate': '2023-07-20T00:30:36+00:00', 'ptex.fullbanner': 'This is pdfTeX, Version 3.141592653-2.6-1.40.25 (TeX Live 2023) kpathsea version 6.3.5', 'subject': '', 'title': '', 'trapped': '/False', 'source': 'C:\\\\Users\\\\<USER>\\\\agenticai-2.0\\\\data\\\\llama2.pdf', 'total_pages': 77, 'page': 76, 'page_label': '77'}, page_content='architecture. The tuned versions use supervised fine-tuning (SFT) and reinforce-\\nment learning with human feedback (RLHF) to align to human preferences for\\nhelpfulness and safety.\\nModel Dates Llama 2was trained between January 2023 and July 2023.\\nStatus This is a static model trained on an offline dataset. Future versions of the tuned\\nmodels will be released as we improve model safety with community feedback.\\nLicense A custom commercial license is available at:ai.meta.com/resources/'),\n", " Document(id='2a350012-aa07-453a-b6ab-fb38a22b7bbe', metadata={'producer': 'pdfTeX-1.40.25', 'creator': 'LaTeX with hyperref', 'creationdate': '2023-07-20T00:30:36+00:00', 'author': '', 'keywords': '', 'moddate': '2023-07-20T00:30:36+00:00', 'ptex.fullbanner': 'This is pdfTeX, Version 3.141592653-2.6-1.40.25 (TeX Live 2023) kpathsea version 6.3.5', 'subject': '', 'title': '', 'trapped': '/False', 'source': 'C:\\\\Users\\\\<USER>\\\\agenticai-2.0\\\\data\\\\llama2.pdf', 'total_pages': 77, 'page': 76, 'page_label': '77'}, page_content='Llama 2is a new technology that carries risks with use. Testing conducted to date has been in\\nEnglish, and has not covered, nor could it cover all scenarios. For these reasons, as with all LLMs,\\nLlama 2’s potential outputs cannot be predicted in advance, and the model may in some instances\\nproduce inaccurate or objectionable responses to user prompts. Therefore, before deploying any\\napplications ofLlama 2, developers should perform safety testing and tuning tailored to their'),\n", " Document(id='7debd417-a192-4b62-ad1f-9139e938d73d', metadata={'producer': 'pdfTeX-1.40.25', 'creator': 'LaTeX with hyperref', 'creationdate': '2023-07-20T00:30:36+00:00', 'author': '', 'keywords': '', 'moddate': '2023-07-20T00:30:36+00:00', 'ptex.fullbanner': 'This is pdfTeX, Version 3.141592653-2.6-1.40.25 (TeX Live 2023) kpathsea version 6.3.5', 'subject': '', 'title': '', 'trapped': '/False', 'source': 'C:\\\\Users\\\\<USER>\\\\agenticai-2.0\\\\data\\\\llama2.pdf', 'total_pages': 77, 'page': 70, 'page_label': '71'}, page_content='Llama 1\\n7B 16.65 30.72 26.82 16.58 26.49 22.27 17.16 19.71 28.67 21.71 29.80 23.01 19.37\\n13B 18.80 32.03 25.18 14.72 28.54 21.11 18.76 15.71 30.42 20.52 27.15 25.21 21.85\\n33B 16.87 32.24 21.53 16.24 28.54 22.04 19.91 18.27 29.88 18.13 25.90 24.53 19.37\\n65B 14.27 31.59 21.90 14.89 23.51 22.27 17.16 18.91 28.40 19.32 28.71 22.00 20.03\\nLlama 2\\n7B 16.53 31.15 22.63 15.74 26.87 19.95 15.79 19.55 25.03 18.92 21.53 22.34 20.20'),\n", " Document(id='e0a2dfde-4195-418c-ac54-c71af6db1243', metadata={'producer': 'pdfTeX-1.40.25', 'creator': 'LaTeX with hyperref', 'creationdate': '2023-07-20T00:30:36+00:00', 'author': '', 'keywords': '', 'moddate': '2023-07-20T00:30:36+00:00', 'ptex.fullbanner': 'This is pdfTeX, Version 3.141592653-2.6-1.40.25 (TeX Live 2023) kpathsea version 6.3.5', 'subject': '', 'title': '', 'trapped': '/False', 'source': 'C:\\\\Users\\\\<USER>\\\\agenticai-2.0\\\\data\\\\llama2.pdf', 'total_pages': 77, 'page': 17, 'page_label': '18'}, page_content='we usegpt-3.5-turbo-0301 model in all generations. For PaLM, we use thechat-bison-001 model in all\\ngenerations. The final prompt count for human evaluations for each model is shown in Table 32. See more\\nmethodology details in Appendix, Section A.3.7. The following section shows helpfulness results; safety\\nresults are presented in Section 4.4.\\nResults. As shown in Figure 12,Llama 2-Chatmodels outperform open-source models by a significant')]"]}, "execution_count": 79, "metadata": {}, "output_type": "execute_result"}], "source": ["retriever.invoke(\"what is llama model?\")"]}, {"cell_type": "code", "execution_count": 80, "id": "6206c1a3", "metadata": {}, "outputs": [], "source": ["from langchain_google_genai import ChatGoogleGenerativeAI\n", "model=ChatGoogleGenerativeAI(model='gemini-1.5-flash')"]}, {"cell_type": "code", "execution_count": 81, "id": "3f475cad", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\agenticai-2.0\\env\\lib\\site-packages\\langsmith\\client.py:272: LangSmithMissingAPIKeyWarning: API key must be provided when using hosted LangSmith API\n", "  warnings.warn(\n"]}], "source": ["from langchain import hub\n", "prompt = hub.pull(\"rlm/rag-prompt\")"]}, {"cell_type": "code", "execution_count": 82, "id": "7d3e73d3", "metadata": {}, "outputs": [], "source": ["import pprint\n"]}, {"cell_type": "code", "execution_count": 83, "id": "6c1b3524", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[HumanMessagePromptTemplate(prompt=PromptTemplate(input_variables=['context', 'question'], input_types={}, partial_variables={}, template=\"You are an assistant for question-answering tasks. Use the following pieces of retrieved context to answer the question. If you don't know the answer, just say that you don't know. Use three sentences maximum and keep the answer concise.\\nQuestion: {question} \\nContext: {context} \\nAnswer:\"), additional_kwargs={})]\n"]}], "source": ["pprint.pprint(prompt.messages)"]}, {"cell_type": "markdown", "id": "5d733fc6", "metadata": {}, "source": ["[HumanMessagePromptTemplate(prompt=PromptTemplate(input_variables=['context', 'question'], input_types={}, partial_variables={}, template=\"You are an assistant for question-answering tasks. Use the following pieces of retrieved context to answer the question. If you don't know the answer, just say that you don't know. Use three sentences maximum and keep the answer concise.\\nQuestion: {question} \\nContext: {context} \\nAnswer:\"), additional_kwargs={})]"]}, {"cell_type": "code", "execution_count": 84, "id": "1af11f84", "metadata": {}, "outputs": [], "source": ["from langchain_core.output_parsers import StrOutputParser\n", "from langchain_core.runnables import RunnablePassthrough"]}, {"cell_type": "code", "execution_count": null, "id": "4e44d99e", "metadata": {}, "outputs": [], "source": ["context(retriever),prompt(hub),model(google),parser(langchain)"]}, {"cell_type": "code", "execution_count": 86, "id": "a10435ec", "metadata": {}, "outputs": [], "source": ["def format_docs(docs):\n", "    return \"\\n\\n\".join(doc.page_content for doc in docs)\n", "    "]}, {"cell_type": "code", "execution_count": 87, "id": "39a<PERSON><PERSON><PERSON>", "metadata": {}, "outputs": [], "source": ["rag_chain = (\n", "    {\"context\": retriever | format_docs, \"question\": RunnablePassthrough()}\n", "    | prompt\n", "    | model\n", "    | StrOutputParser()\n", ")"]}, {"cell_type": "code", "execution_count": 88, "id": "5dc3c2c2", "metadata": {}, "outputs": [{"data": {"text/plain": ["'Llama is a large language model developed by Meta.  It has various versions, including Llama 1 and Llama 2, with different parameter sizes.  Llama 2 is designed for commercial and research use and is available under a custom commercial license.'"]}, "execution_count": 88, "metadata": {}, "output_type": "execute_result"}], "source": ["rag_chain.invoke(\"what is llama model?\")"]}, {"cell_type": "code", "execution_count": null, "id": "82725f7f", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "env", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.16"}}, "nbformat": 4, "nbformat_minor": 5}