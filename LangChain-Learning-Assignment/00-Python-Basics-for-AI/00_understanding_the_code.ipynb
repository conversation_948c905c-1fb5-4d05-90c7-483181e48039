{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 🐍 Understanding Python Code for AI - Step by Step\n", "\n", "## 🎯 What We'll Learn\n", "This notebook explains EVERY line of code, assuming you know nothing about programming.\n", "\n", "## 🧠 Think of This Like Learning to Drive\n", "- First, we'll learn what each part of the car does\n", "- Then, we'll learn how to use each part\n", "- Finally, we'll put it all together to drive"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 1: Understanding Imports (Getting Your Tools)\n", "\n", "**What imports do**: They bring in pre-written code that other people created\n", "\n", "**Real-world analogy**: Like going to a hardware store to get tools instead of making them yourself\n", "\n", "**Why we need this**: We don't want to write thousands of lines of code when someone already did it"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Let's import our tools one by one and explain each\n", "\n", "# 1. Import 'os' - this helps us work with our computer's operating system\n", "import os\n", "print(\"✅ Imported 'os' - now we can read files, environment variables, etc.\")\n", "\n", "# 2. Import 'load_dotenv' from the 'dotenv' package\n", "# This helps us load secret keys from a .env file\n", "from dotenv import load_dotenv\n", "print(\"✅ Imported 'load_dotenv' - now we can load secret API keys safely\")\n", "\n", "# 3. I<PERSON>rt ChatOpenAI from langchain_openai\n", "# This is a pre-built class that knows how to talk to OpenAI\n", "from langchain_openai import ChatOpenAI\n", "print(\"✅ Imported 'ChatOpenAI' - now we can connect to OpenAI's AI models\")\n", "\n", "# 4. Import ChatPromptTemplate from langchain_core.prompts\n", "# This helps us create structured prompts for AI\n", "from langchain_core.prompts import ChatPromptTemplate\n", "print(\"✅ Imported 'ChatPromptTemplate' - now we can create reusable prompt templates\")\n", "\n", "# 5. Import StrOutputParser from langchain_core.output_parsers\n", "# This helps us clean up AI responses\n", "from langchain_core.output_parsers import StrOutputParser\n", "print(\"✅ Imported 'StrOutputParser' - now we can get clean text from AI responses\")\n", "\n", "print(\"\\n🎉 All tools imported! We're ready to build AI applications.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 2: Understanding Variables (Labeled Boxes)\n", "\n", "**What variables do**: Store information and give it a name so we can use it later\n", "\n", "**Real-world analogy**: Like putting your phone in a box labeled \"phone\" so you always know where it is\n", "\n", "**Syntax**: `variable_name = value`"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Let's create some variables and see how they work\n", "\n", "# 1. String variable (text)\n", "my_name = \"<PERSON>\"\n", "print(f\"String variable: {my_name}\")\n", "print(f\"Type: {type(my_name)}\")\n", "\n", "# 2. Integer variable (whole number)\n", "my_age = 25\n", "print(f\"\\nInteger variable: {my_age}\")\n", "print(f\"Type: {type(my_age)}\")\n", "\n", "# 3. Float variable (decimal number)\n", "temperature = 0.7\n", "print(f\"\\nFloat variable: {temperature}\")\n", "print(f\"Type: {type(temperature)}\")\n", "\n", "# 4. Boolean variable (True/False)\n", "is_learning = True\n", "print(f\"\\nBoolean variable: {is_learning}\")\n", "print(f\"Type: {type(is_learning)}\")\n", "\n", "# 5. Using variables together\n", "greeting = f\"Hello, my name is {my_name} and I am {my_age} years old.\"\n", "print(f\"\\nCombined: {greeting}\")\n", "\n", "print(\"\\n💡 Variables let us store and reuse information!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 3: Understanding Functions (Mini-Recipes)\n", "\n", "**What functions do**: Take some input, do something with it, and give back a result\n", "\n", "**Real-world analogy**: Like a coffee machine - you put in coffee beans and water, it processes them, and gives you coffee\n", "\n", "**Syntax**: \n", "```python\n", "def function_name(input_parameters):\n", "    # do something\n", "    return result\n", "```"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Let's create and use some functions\n", "\n", "# 1. Simple function with no parameters\n", "def say_hello():\n", "    return \"Hello, <PERSON>!\"\n", "\n", "# Call the function\n", "result1 = say_hello()\n", "print(f\"Function with no parameters: {result1}\")\n", "\n", "# 2. Function with one parameter\n", "def greet_person(name):\n", "    return f\"Hello, {name}!\"\n", "\n", "# Call the function with an argument\n", "result2 = greet_person(\"<PERSON>\")\n", "print(f\"Function with one parameter: {result2}\")\n", "\n", "# 3. Function with multiple parameters\n", "def create_ai_prompt(role, task, style):\n", "    prompt = f\"You are a {role}. Please {task} in a {style} manner.\"\n", "    return prompt\n", "\n", "# Call the function with multiple arguments\n", "ai_instruction = create_ai_prompt(\"teacher\", \"explain quantum physics\", \"simple\")\n", "print(f\"\\nAI Prompt: {ai_instruction}\")\n", "\n", "# 4. Function that calls other functions\n", "def process_user_request(user_name, topic):\n", "    greeting = greet_person(user_name)\n", "    prompt = create_ai_prompt(\"expert\", f\"explain {topic}\", \"detailed\")\n", "    return f\"{greeting} {prompt}\"\n", "\n", "final_result = process_user_request(\"Charlie\", \"machine learning\")\n", "print(f\"\\nCombined result: {final_result}\")\n", "\n", "print(\"\\n💡 Functions help us organize code and avoid repetition!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 4: Understanding Classes (Blueprints)\n", "\n", "**What classes do**: Create templates for making objects with specific properties and abilities\n", "\n", "**Real-world analogy**: Like a blueprint for a house - you can build many houses from the same blueprint\n", "\n", "**Why we use classes**: To organize related data and functions together"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Let's create a simple class to understand the concept\n", "\n", "class AIAssistant:\n", "    \"\"\"A simple AI assistant class to demonstrate concepts\"\"\"\n", "    \n", "    # This runs when we create a new AI assistant\n", "    def __init__(self, name, personality):\n", "        self.name = name                    # Store the assistant's name\n", "        self.personality = personality      # Store the assistant's personality\n", "        self.conversation_count = 0         # Keep track of conversations\n", "        print(f\"✅ Created AI assistant named {self.name} with {self.personality} personality\")\n", "    \n", "    # This is something the AI assistant can do\n", "    def greet_user(self, user_name):\n", "        self.conversation_count += 1\n", "        if self.personality == \"friendly\":\n", "            return f\"Hi there, {user_name}! I'm {self.name}, and I'm excited to help you!\"\n", "        elif self.personality == \"professional\":\n", "            return f\"Good day, {user_name}. I am {self.name}, your AI assistant.\"\n", "        else:\n", "            return f\"Hello, {user_name}. I'm {self.name}.\"\n", "    \n", "    # Another thing the AI assistant can do\n", "    def get_stats(self):\n", "        return f\"{self.name} has had {self.conversation_count} conversations\"\n", "\n", "# Now let's create actual AI assistants using our class\n", "print(\"Creating AI assistants...\\n\")\n", "\n", "# Create a friendly assistant\n", "friendly_ai = AIAssistant(\"Buddy\", \"friendly\")\n", "\n", "# Create a professional assistant\n", "professional_ai = AIAssistant(\"Alex\", \"professional\")\n", "\n", "print(\"\\nTesting the assistants...\\n\")\n", "\n", "# Test the friendly assistant\n", "friendly_greeting = friendly_ai.greet_user(\"<PERSON>\")\n", "print(f\"Friendly AI: {friendly_greeting}\")\n", "\n", "# Test the professional assistant\n", "professional_greeting = professional_ai.greet_user(\"<PERSON>\")\n", "print(f\"Professional AI: {professional_greeting}\")\n", "\n", "# Check stats\n", "print(f\"\\nStats: {friendly_ai.get_stats()}\")\n", "print(f\"Stats: {professional_ai.get_stats()}\")\n", "\n", "print(\"\\n💡 Classes let us create multiple objects with the same structure but different data!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 5: Understanding How <PERSON><PERSON><PERSON><PERSON> Classes Work\n", "\n", "**What we're doing**: Now let's see how the LangChain classes work, step by step\n", "\n", "**Why this matters**: Understanding this will help you use <PERSON><PERSON><PERSON><PERSON> effectively"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# First, let's load our environment variables\n", "print(\"Step 1: Loading environment variables...\")\n", "load_dotenv()\n", "print(\"✅ Environment variables loaded from .env file\")\n", "\n", "# Set the API key for this session\n", "os.environ[\"OPENAI_API_KEY\"] = os.getenv(\"OPENAI_API_KEY\")\n", "print(\"✅ OpenAI API key set for this session\")\n", "\n", "print(\"\\n\" + \"=\"*50)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Step 2: Create a ChatOpenAI object\n", "print(\"Step 2: Creating ChatOpenAI object...\")\n", "\n", "# This creates an instance of the ChatOpenAI class\n", "# Think of it like hiring an AI assistant with specific settings\n", "llm = ChatOpenAI(\n", "    model=\"gpt-3.5-turbo\",    # Which AI brain to use\n", "    temperature=0.7,          # How creative (0=boring, 1=very creative)\n", "    max_tokens=150           # Maximum length of response\n", ")\n", "\n", "print(\"✅ ChatOpenAI object created!\")\n", "print(f\"   Model: {llm.model_name}\")\n", "print(f\"   Temperature: {llm.temperature}\")\n", "print(f\"   Max tokens: {llm.max_tokens}\")\n", "\n", "# What this object can do:\n", "print(\"\\n🔧 This object has methods (functions) we can use:\")\n", "print(\"   - llm.invoke(message) - Send a message to AI\")\n", "print(\"   - llm.stream(message) - Get streaming response\")\n", "print(\"   - llm.batch(messages) - Send multiple messages\")\n", "\n", "print(\"\\n\" + \"=\"*50)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Step 3: Create a ChatPromptTemplate object\n", "print(\"Step 3: Creating ChatPromptTemplate object...\")\n", "\n", "# This creates a template for structuring our prompts\n", "# Think of it like a form letter where you fill in the blanks\n", "prompt = ChatPromptTemplate.from_messages([\n", "    # System message - tells the AI how to behave\n", "    (\"system\", \"You are a helpful {role}. Always be {personality} and {detail_level}.\"),\n", "    # User message - the actual question\n", "    (\"user\", \"{question}\")\n", "])\n", "\n", "print(\"✅ ChatPromptTemplate object created!\")\n", "print(f\"   Number of messages: {len(prompt.messages)}\")\n", "print(f\"   Input variables: {prompt.input_variables}\")\n", "\n", "# Let's see what the template looks like\n", "print(\"\\n📝 Template structure:\")\n", "for i, message in enumerate(prompt.messages):\n", "    print(f\"   Message {i+1}: {message.prompt.template}\")\n", "\n", "print(\"\\n\" + \"=\"*50)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Step 4: Create a StrOutputParser object\n", "print(\"Step 4: Creating StrOutputParser object...\")\n", "\n", "# This creates a parser that extracts clean text from AI responses\n", "# Think of it like a filter that removes everything except the answer\n", "output_parser = StrOutputParser()\n", "\n", "print(\"✅ StrOutputParser object created!\")\n", "print(\"   Purpose: Extract clean text from AI response objects\")\n", "print(\"   Input: Complex AI response object\")\n", "print(\"   Output: Simple string with just the answer\")\n", "\n", "print(\"\\n\" + \"=\"*50)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Step 5: Create a Chain (Connect Everything Together)\n", "print(\"Step 5: Creating a chain...\")\n", "\n", "# The pipe operator (|) connects components together\n", "# Data flows from left to right: prompt → model → parser\n", "chain = prompt | llm | output_parser\n", "\n", "print(\"✅ Chain created!\")\n", "print(\"\\n🔄 Data flow:\")\n", "print(\"   1. Input goes to prompt template\")\n", "print(\"   2. Formatted prompt goes to AI model\")\n", "print(\"   3. AI response goes to output parser\")\n", "print(\"   4. Clean text comes out\")\n", "\n", "print(\"\\n💡 This is like an assembly line in a factory!\")\n", "\n", "print(\"\\n\" + \"=\"*50)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Step 6: Use the Chain\n", "print(\"Step 6: Using the chain...\")\n", "\n", "# Define our input\n", "user_input = {\n", "    \"role\": \"science teacher\",\n", "    \"personality\": \"enthusiastic\",\n", "    \"detail_level\": \"simple explanations\",\n", "    \"question\": \"What is photosynthesis?\"\n", "}\n", "\n", "print(f\"📥 Input: {user_input}\")\n", "print(\"\\n🔄 Processing through chain...\")\n", "\n", "# Let's trace what happens at each step\n", "print(\"\\n1️⃣ Prompt template processing:\")\n", "formatted_messages = prompt.format_messages(**user_input)\n", "for msg in formatted_messages:\n", "    print(f\"   {msg.type}: {msg.content}\")\n", "\n", "print(\"\\n2️⃣ Sending to AI model...\")\n", "ai_response = llm.invoke(formatted_messages)\n", "print(f\"   AI response type: {type(ai_response)}\")\n", "print(f\"   AI response content: {ai_response.content[:100]}...\")\n", "\n", "print(\"\\n3️⃣ Parsing output...\")\n", "final_result = output_parser.parse(ai_response)\n", "print(f\"   Parsed result type: {type(final_result)}\")\n", "print(f\"   Final clean result: {final_result}\")\n", "\n", "print(\"\\n\" + \"=\"*50)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Step 7: Using the Chain the Easy Way\n", "print(\"Step 7: Using the chain the easy way...\")\n", "\n", "# Instead of doing each step manually, we can use the chain directly\n", "# This does all the steps automatically\n", "easy_result = chain.invoke(user_input)\n", "\n", "print(\"✅ Chain executed automatically!\")\n", "print(f\"📤 Final result: {easy_result}\")\n", "\n", "print(\"\\n💡 The chain did all the work for us:\")\n", "print(\"   ✅ Formatted the prompt\")\n", "print(\"   ✅ Sent it to the AI\")\n", "print(\"   ✅ Got the response\")\n", "print(\"   ✅ Parsed the output\")\n", "print(\"   ✅ Returned clean text\")\n", "\n", "print(\"\\n🎉 This is the power of LangChain - complex operations made simple!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🎯 Summary: Why We Use This Architecture\n", "\n", "### 1. **Separation of Concerns**\n", "- **Prompt Template**: Handles formatting\n", "- **AI Model**: <PERSON><PERSON> thinking\n", "- **Output Parser**: Handles cleaning\n", "\n", "### 2. **Reusability**\n", "- Same template with different models\n", "- Same model with different templates\n", "- Same parser with different chains\n", "\n", "### 3. **Maintainability**\n", "- Easy to debug (isolate which component has issues)\n", "- Easy to update (change one component without affecting others)\n", "- Easy to test (test each component separately)\n", "\n", "### 4. **Scalability**\n", "- Can handle multiple requests\n", "- Can add more components easily\n", "- Can optimize individual components\n", "\n", "## 🔍 The Magic of the Pipe Operator (|)\n", "\n", "```python\n", "chain = prompt | llm | output_parser\n", "```\n", "\n", "This is equivalent to:\n", "```python\n", "def manual_chain(input_data):\n", "    step1 = prompt.format_messages(**input_data)\n", "    step2 = llm.invoke(step1)\n", "    step3 = output_parser.parse(step2)\n", "    return step3\n", "```\n", "\n", "The pipe operator makes it much cleaner and easier to read!\n", "\n", "## 🚀 Next Steps\n", "\n", "Now you understand:\n", "- ✅ How imports work\n", "- ✅ How variables store data\n", "- ✅ How functions organize code\n", "- ✅ How classes create objects\n", "- ✅ How LangChain components work together\n", "- ✅ How chains create AI workflows\n", "\n", "You're ready to start building AI applications! 🎉"]}], "metadata": {"kernelspec": {"display_name": "langchain_learning", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.0"}}, "nbformat": 4, "nbformat_minor": 2}