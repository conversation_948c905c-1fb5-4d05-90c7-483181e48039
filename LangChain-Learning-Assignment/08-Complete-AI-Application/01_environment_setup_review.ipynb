# Check current environment
import sys
import os

print("🔍 Environment Information:")
print(f"Python version: {sys.version}")
print(f"Python executable: {sys.executable}")
print(f"Current working directory: {os.getcwd()}")

# Check if we're in a conda environment
conda_env = os.environ.get('CONDA_DEFAULT_ENV')
if conda_env:
    print(f"✅ Conda environment: {conda_env}")
else:
    print("⚠️ Not in a conda environment - consider creating one")

print("\n💡 If you need to create a conda environment, run:")
print("   conda create -n ai_app_env python=3.10")
print("   conda activate ai_app_env")

# Function to safely install and import packages
def install_and_test_package(package_name, import_name=None):
    """
    Install a package and test if it can be imported
    
    Args:
        package_name: Name to install with pip
        import_name: Name to use for import (if different)
    """
    import subprocess
    import importlib
    
    # Use package_name for import if import_name not specified
    if import_name is None:
        import_name = package_name
    
    try:
        # Try to import first
        importlib.import_module(import_name)
        print(f"✅ {package_name} already installed and working")
        return True
    except ImportError:
        print(f"📦 Installing {package_name}...")
        try:
            # Install the package
            subprocess.check_call([sys.executable, "-m", "pip", "install", package_name])
            
            # Test import after installation
            importlib.import_module(import_name)
            print(f"✅ {package_name} installed and working")
            return True
        except Exception as e:
            print(f"❌ Failed to install {package_name}: {e}")
            return False

# Test the function with a simple package
print("🧪 Testing package installation function...")
install_and_test_package("requests")

# Install core LangChain packages
print("📦 Installing Core LangChain Packages...")
print("=" * 50)

core_packages = [
    ("langchain", "langchain"),
    ("langchain-openai", "langchain_openai"),
    ("langchain-groq", "langchain_groq"),
    ("langchain-core", "langchain_core"),
    ("langchain-community", "langchain_community")
]

for package, import_name in core_packages:
    success = install_and_test_package(package, import_name)
    if not success:
        print(f"⚠️ Warning: {package} installation failed")

print("\n✅ Core LangChain packages installation complete!")

# Install utility packages
print("🛠️ Installing Utility Packages...")
print("=" * 50)

utility_packages = [
    ("python-dotenv", "dotenv"),
    ("pydantic", "pydantic"),
    ("requests", "requests"),
    ("pandas", "pandas"),
    ("numpy", "numpy")
]

for package, import_name in utility_packages:
    install_and_test_package(package, import_name)

print("\n✅ Utility packages installation complete!")

# Install document processing packages
print("📄 Installing Document Processing Packages...")
print("=" * 50)

document_packages = [
    ("pypdf", "pypdf"),
    ("python-docx", "docx"),
    ("beautifulsoup4", "bs4"),
    ("unstructured", "unstructured")
]

for package, import_name in document_packages:
    install_and_test_package(package, import_name)

print("\n✅ Document processing packages installation complete!")

# Install vector search packages
print("🔍 Installing Vector Search Packages...")
print("=" * 50)

vector_packages = [
    ("chromadb", "chromadb"),
    ("sentence-transformers", "sentence_transformers"),
    ("faiss-cpu", "faiss")
]

for package, import_name in vector_packages:
    install_and_test_package(package, import_name)

print("\n✅ Vector search packages installation complete!")

# Install UI packages
print("🖥️ Installing User Interface Packages...")
print("=" * 50)

ui_packages = [
    ("streamlit", "streamlit"),
    ("gradio", "gradio")
]

for package, import_name in ui_packages:
    install_and_test_package(package, import_name)

print("\n✅ User interface packages installation complete!")

# Check if .env file exists
import os
from pathlib import Path

env_file = Path(".env")

print("🔐 Environment Variables Setup")
print("=" * 40)

if env_file.exists():
    print("✅ .env file found")
    
    # Load and check environment variables
    from dotenv import load_dotenv
    load_dotenv()
    
    # Check for required API keys
    required_keys = [
        "OPENAI_API_KEY",
        "GROQ_API_KEY",
        "LANGCHAIN_API_KEY",
        "LANGCHAIN_PROJECT"
    ]
    
    print("\n🔍 Checking API keys:")
    for key in required_keys:
        value = os.getenv(key)
        if value:
            # Show only first 10 characters for security
            masked_value = value[:10] + "..." if len(value) > 10 else value
            print(f"  ✅ {key}: {masked_value}")
        else:
            print(f"  ❌ {key}: Not found")
            
else:
    print("❌ .env file not found")
    print("\n📝 Creating template .env file...")
    
    template_content = '''# API Keys for AI Application
# Replace the placeholder values with your actual API keys

# OpenAI API Key (get from https://platform.openai.com/api-keys)
OPENAI_API_KEY="your-openai-api-key-here"

# Groq API Key (get from https://console.groq.com/keys)
GROQ_API_KEY="your-groq-api-key-here"

# LangChain API Key (get from https://smith.langchain.com/)
LANGCHAIN_API_KEY="your-langchain-api-key-here"
LANGCHAIN_PROJECT="AI-Application"
LANGCHAIN_TRACING_V2="true"

# Optional: Other API keys
HUGGINGFACE_API_KEY="your-huggingface-key-here"
'''
    
    with open(".env", "w") as f:
        f.write(template_content)
    
    print("✅ Template .env file created")
    print("\n⚠️ IMPORTANT: Please edit the .env file and add your actual API keys")
    print("\n🔗 Get API keys from:")
    print("  • OpenAI: https://platform.openai.com/api-keys")
    print("  • Groq: https://console.groq.com/keys")
    print("  • LangChain: https://smith.langchain.com/")

# Test OpenAI connection
print("🤖 Testing OpenAI Connection...")
print("=" * 40)

try:
    from langchain_openai import ChatOpenAI
    from dotenv import load_dotenv
    
    # Load environment variables
    load_dotenv()
    
    # Set environment variables for this session
    os.environ["OPENAI_API_KEY"] = os.getenv("OPENAI_API_KEY")
    
    # Create OpenAI model
    openai_model = ChatOpenAI(
        model="gpt-3.5-turbo",
        temperature=0.7,
        max_tokens=50  # Short response for testing
    )
    
    print(f"✅ OpenAI model created: {openai_model.model_name}")
    
    # Test with a simple question
    test_response = openai_model.invoke("Say 'Hello from OpenAI!' in a friendly way.")
    print(f"📝 Test response: {test_response.content}")
    print("✅ OpenAI connection successful!")
    
except Exception as e:
    print(f"❌ OpenAI connection failed: {e}")
    print("💡 Check your OPENAI_API_KEY in the .env file")

# Test Groq connection
print("\n⚡ Testing Groq Connection...")
print("=" * 40)

try:
    from langchain_groq import ChatGroq
    
    # Set Groq API key
    os.environ["GROQ_API_KEY"] = os.getenv("GROQ_API_KEY")
    
    # Create Groq model
    groq_model = ChatGroq(
        model="gemma2-9b-it",
        temperature=0.7,
        max_tokens=50  # Short response for testing
    )
    
    print(f"✅ Groq model created: {groq_model.model_name}")
    
    # Test with a simple question
    test_response = groq_model.invoke("Say 'Hello from Groq!' in a friendly way.")
    print(f"📝 Test response: {test_response.content}")
    print("✅ Groq connection successful!")
    
except Exception as e:
    print(f"❌ Groq connection failed: {e}")
    print("💡 Check your GROQ_API_KEY in the .env file")

# Test LangChain tracing
print("\n📊 Testing LangChain Tracing...")
print("=" * 40)

try:
    # Set LangChain environment variables
    os.environ["LANGCHAIN_API_KEY"] = os.getenv("LANGCHAIN_API_KEY")
    os.environ["LANGCHAIN_PROJECT"] = os.getenv("LANGCHAIN_PROJECT")
    os.environ["LANGCHAIN_TRACING_V2"] = "true"
    
    print(f"✅ LangChain project: {os.getenv('LANGCHAIN_PROJECT')}")
    print(f"✅ Tracing enabled: {os.getenv('LANGCHAIN_TRACING_V2')}")
    print("📊 Your AI interactions will be tracked in LangSmith")
    
except Exception as e:
    print(f"❌ LangChain tracing setup failed: {e}")
    print("💡 Check your LANGCHAIN_API_KEY in the .env file")

# Test basic chain functionality
print("\n🔗 Testing Basic Chain Functionality...")
print("=" * 40)

try:
    from langchain_core.prompts import ChatPromptTemplate
    from langchain_core.output_parsers import StrOutputParser
    
    # Create a simple chain
    prompt = ChatPromptTemplate.from_messages([
        ("system", "You are a helpful assistant. Be brief and friendly."),
        ("user", "{question}")
    ])
    
    parser = StrOutputParser()
    
    # Create chain with Groq (faster for testing)
    chain = prompt | groq_model | parser
    
    print("✅ Chain created successfully")
    
    # Test the chain
    result = chain.invoke({"question": "What is 2+2? Just give the number."})
    print(f"📝 Chain test result: {result}")
    print("✅ Chain functionality working!")
    
except Exception as e:
    print(f"❌ Chain test failed: {e}")
    print("💡 Check that all previous tests passed")

# Create requirements.txt file
print("📋 Creating requirements.txt file...")

requirements_content = '''# Core LangChain packages
langchain>=0.1.0
langchain-openai>=0.1.0
langchain-groq>=0.1.0
langchain-core>=0.1.0
langchain-community>=0.1.0

# Utility packages
python-dotenv>=1.0.0
pydantic>=2.0.0
requests>=2.31.0
pandas>=2.0.0
numpy>=1.24.0

# Document processing
pypdf>=3.0.0
python-docx>=0.8.11
beautifulsoup4>=4.12.0
unstructured>=0.10.0

# Vector search and embeddings
chromadb>=0.4.0
sentence-transformers>=2.2.0
faiss-cpu>=1.7.0

# User interface
streamlit>=1.28.0
gradio>=3.50.0

# Development tools
jupyter>=1.0.0
ipykernel>=6.25.0
'''

with open("requirements.txt", "w") as f:
    f.write(requirements_content)

print("✅ requirements.txt created")
print("\n📝 Contents:")
print(requirements_content)

print("\n💡 Others can now install all dependencies with:")
print("   pip install -r requirements.txt")