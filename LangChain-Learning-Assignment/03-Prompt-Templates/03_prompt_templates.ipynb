# Import required packages
import os
from dotenv import load_dotenv
from langchain_core.prompts import Chat<PERSON>romptTemplate, PromptTemplate
from langchain_groq import Chat<PERSON><PERSON><PERSON>
from langchain_openai import ChatOpenAI

# Load environment variables
load_dotenv()
os.environ["GROQ_API_KEY"] = os.getenv("GROQ_API_KEY")
os.environ["OPENAI_API_KEY"] = os.getenv("OPENAI_API_KEY")

# Create our AI model (using Groq for speed)
model = ChatGroq(model="gemma2-9b-it", temperature=0.7)

print("✅ Environment setup complete!")
print("🤖 AI model ready for prompt template experiments!")

# Create a simple prompt template
simple_template = PromptTemplate(
    template="Explain {topic} in simple terms for a {audience}.",
    input_variables=["topic", "audience"]
)

print("📝 Template created!")
print(f"Template: {simple_template.template}")
print(f"Variables: {simple_template.input_variables}")

# Test the template
filled_prompt = simple_template.format(
    topic="machine learning",
    audience="10-year-old child"
)

print(f"\n🎯 Filled prompt: {filled_prompt}")

# Create a chat prompt template
chat_template = ChatPromptTemplate.from_messages([
    ("system", "You are a helpful {role}. Always be {personality} and provide {detail_level} explanations."),
    ("user", "{question}")
])

print("💬 Chat template created!")
print("Template structure:")
for i, message in enumerate(chat_template.messages):
    print(f"  {i+1}. {message.prompt.template} (type: {type(message).__name__})")

# Test the chat template
filled_messages = chat_template.format_messages(
    role="science teacher",
    personality="enthusiastic",
    detail_level="detailed",
    question="How do plants make food?"
)

print("\n🎯 Filled messages:")
for msg in filled_messages:
    print(f"  {msg.type}: {msg.content}")

# Test the chat template with our AI model
response = model.invoke(filled_messages)

print("🤖 AI Response using template:")
print("=" * 50)
print(response.content)
print("=" * 50)

# Now let's try the same question without a template
simple_question = "How do plants make food?"
simple_response = model.invoke(simple_question)

print("\n🤖 AI Response without template:")
print("=" * 50)
print(simple_response.content)
print("=" * 50)

print("\n💡 Notice how the template made the response more structured and enthusiastic!")

# Template 1: Customer Service Bot
customer_service_template = ChatPromptTemplate.from_messages([
    ("system", "You are a friendly customer service representative for {company}. "
               "Always be polite, helpful, and professional. "
               "If you can't help, offer to escalate to a human agent."),
    ("user", "{customer_issue}")
])

# Template 2: Math Tutor
math_tutor_template = ChatPromptTemplate.from_messages([
    ("system", "You are a patient math tutor for {grade_level} students. "
               "Break down problems step-by-step. "
               "Use encouraging language and provide examples."),
    ("user", "Help me with this math problem: {problem}")
])

# Template 3: Creative Writing Assistant
creative_writing_template = ChatPromptTemplate.from_messages([
    ("system", "You are a creative writing coach specializing in {genre}. "
               "Help writers improve their {writing_aspect}. "
               "Be encouraging and provide specific, actionable feedback."),
    ("user", "{writing_request}")
])

print("✅ Created 3 specialized templates:")
print("1. 🏢 Customer Service Bot")
print("2. 📚 Math Tutor")
print("3. ✍️ Creative Writing Assistant")

# Test Customer Service Template
print("🏢 Testing Customer Service Template:")
cs_messages = customer_service_template.format_messages(
    company="TechGadgets Inc.",
    customer_issue="My laptop won't turn on after the latest update."
)
cs_response = model.invoke(cs_messages)
print(f"Response: {cs_response.content}")

print("\n" + "="*60)

# Test Math Tutor Template
print("\n📚 Testing Math Tutor Template:")
math_messages = math_tutor_template.format_messages(
    grade_level="5th grade",
    problem="What is 15 × 24?"
)
math_response = model.invoke(math_messages)
print(f"Response: {math_response.content}")

print("\n" + "="*60)

# Test Creative Writing Template
print("\n✍️ Testing Creative Writing Template:")
writing_messages = creative_writing_template.format_messages(
    genre="science fiction",
    writing_aspect="character development",
    writing_request="How can I make my robot character more relatable?"
)
writing_response = model.invoke(writing_messages)
print(f"Response: {writing_response.content}")

# Create a comprehensive template
comprehensive_template = ChatPromptTemplate.from_messages([
    ("system", 
     "You are a {role} with {experience_level} experience. "
     "Your audience is {audience} with {knowledge_level} knowledge of {subject}. "
     "Your communication style should be {tone} and {formality_level}. "
     "Provide {response_length} responses with {detail_level} detail."),
    ("user", "{user_request}")
])

print("🎛️ Comprehensive template created with 8 variables!")
print("Variables:", comprehensive_template.input_variables)

# Test with different configurations
configs = [
    {
        "role": "software engineer",
        "experience_level": "senior",
        "audience": "beginners",
        "knowledge_level": "basic",
        "subject": "programming",
        "tone": "encouraging",
        "formality_level": "casual",
        "response_length": "concise",
        "detail_level": "high",
        "user_request": "Explain what a function is in programming"
    },
    {
        "role": "university professor",
        "experience_level": "expert",
        "audience": "graduate students",
        "knowledge_level": "advanced",
        "subject": "machine learning",
        "tone": "analytical",
        "formality_level": "formal",
        "response_length": "detailed",
        "detail_level": "technical",
        "user_request": "Explain gradient descent optimization"
    }
]

for i, config in enumerate(configs, 1):
    print(f"\n🧪 Test Configuration {i}:")
    messages = comprehensive_template.format_messages(**config)
    response = model.invoke(messages)
    print(f"Request: {config['user_request']}")
    print(f"Response: {response.content[:200]}...")  # First 200 characters
    print("-" * 50)

# Function to safely use templates
def safe_template_invoke(template, model, **kwargs):
    """
    Safely invoke a template with error handling
    """
    try:
        # Check if all required variables are provided
        missing_vars = set(template.input_variables) - set(kwargs.keys())
        if missing_vars:
            return f"❌ Missing required variables: {missing_vars}"
        
        # Format and invoke
        messages = template.format_messages(**kwargs)
        response = model.invoke(messages)
        return f"✅ Success: {response.content}"
        
    except Exception as e:
        return f"❌ Error: {str(e)}"

# Test with missing variables
print("🧪 Testing error handling:")

# Test 1: Missing variables
result1 = safe_template_invoke(
    customer_service_template, 
    model,
    company="TechCorp"  # Missing customer_issue
)
print(f"Test 1 (missing variable): {result1}")

# Test 2: All variables provided
result2 = safe_template_invoke(
    customer_service_template,
    model,
    company="TechCorp",
    customer_issue="I need help with my account"
)
print(f"\nTest 2 (all variables): {result2[:100]}...")  # First 100 characters

# Task 1: Create your templates here

# 1. Fitness Coach Template
fitness_template = ChatPromptTemplate.from_messages([
    # Your template here...
])

# 2. Travel Guide Template
travel_template = ChatPromptTemplate.from_messages([
    # Your template here...
])

# 3. Recipe Assistant Template
recipe_template = ChatPromptTemplate.from_messages([
    # Your template here...
])

# 4. Study Buddy Template
study_template = ChatPromptTemplate.from_messages([
    # Your template here...
])

print("✅ Created 4 custom templates!")

# Task 2: Test your templates here

# Test scenarios:
test_cases = {
    "fitness": {
        # Your test variables here...
    },
    "travel": {
        # Your test variables here...
    },
    "recipe": {
        # Your test variables here...
    },
    "study": {
        # Your test variables here...
    }
}

# Test each template
# Your testing code here...

# Task 3: Comparison test

question = "How do I lose weight?"

# Without template
no_template_response = model.invoke(question)
print("🤖 Without template:")
print(no_template_response.content)

print("\n" + "="*50 + "\n")

# With your fitness template
# Your code here...

print("\n💡 What differences do you notice?")

# Task 4: Create 3 versions of the same template

# Version 1: Simple and direct
template_v1 = ChatPromptTemplate.from_messages([
    # Your simple version here...
])

# Version 2: Detailed and structured
template_v2 = ChatPromptTemplate.from_messages([
    # Your detailed version here...
])

# Version 3: Creative and engaging
template_v3 = ChatPromptTemplate.from_messages([
    # Your creative version here...
])

# Test all three with the same input
# Your testing code here...

print("Which version worked best and why?")
# Your analysis here...