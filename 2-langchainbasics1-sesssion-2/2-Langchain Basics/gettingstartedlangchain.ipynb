import os
from dotenv import load_dotenv
load_dotenv()


os.getenv("LANGCHAIN_PROJECT")

os.getenv("LANGCHAIN_API_KEY")

os.environ["OPENAI_API_KEY"]=os.getenv("OPENAI_API_KEY")
os.environ["GROQ_API_KEY"]=os.getenv("GROQ_API_KEY")

## Langsmith Tracking And Tracing
os.environ["LANGCHAIN_API_KEY"]=os.getenv("LANGCHAIN_API_KEY")
os.environ["LANGCHAIN_PROJECT"]=os.getenv("LANGCHAIN_PROJECT")
os.environ["LANGCHAIN_TRACING_V2"]="true"

from langchain_openai import ChatOpenAI

llm=ChatOpenAI(model="o1-mini")
print(llm)



result=llm.invoke("What is Agentic AI")
print(result)

print(result.content)

from langchain_groq import ChatGroq
model=ChatGroq(model="qwen-qwq-32b")
model.invoke("Hi My name is <PERSON><PERSON>")

### Prompt Engineering
from langchain_core.prompts import ChatPromptTemplate

prompt=ChatPromptTemplate.from_messages(
    [
        ("system","You are an expert AI Engineer. Provide me answer based on the question"),
        ("user","{input}")
    ]
)
prompt



from langchain_groq import ChatGroq
model=ChatGroq(model="gemma2-9b-it")
model

### chaining
chain=prompt|model
chain

response=chain.invoke({"input":"Can you tell me something about Langsmith"})
print(response.content)

### OutputParser
from langchain_core.output_parsers import StrOutputParser

output_parser=StrOutputParser()

chain=prompt|model|output_parser

response=chain.invoke({"input":"Can you tell me about Langsmith"})
print(response)

from langchain_core.output_parsers import JsonOutputParser

output_parser=JsonOutputParser()
output_parser.get_format_instructions()

### OutputParser
from langchain_core.output_parsers import JsonOutputParser
from langchain_core.prompts import PromptTemplate

output_parser=JsonOutputParser()

prompt=PromptTemplate(
    template="Answer the user query \n {format_instruction}\n {query}\n ",
    input_variables=["query"],
    partial_variables={"format_instruction":output_parser.get_format_instructions()},
)

prompt

chain=prompt|model|output_parser
response=chain.invoke({"query":"Can you tell me about Langsmith?"})
print(response)



### Assisgnment ---Chatprompttemplate

### Prompt Engineering
from langchain_core.prompts import ChatPromptTemplate

prompt=ChatPromptTemplate.from_messages(
    [
        ("system","You are an expert AI Engineer.Provide the response in json.Provide me answer based on the question"),
        ("user","{input}")
    ]
)
prompt

chain=prompt|model|output_parser
response=chain.invoke({"input":"Can you tell me about Langsmith?"})
print(response)

### OutputParser
from langchain_core.output_parsers import XMLOutputParser
output_parser=XMLOutputParser()
from langchain_core.prompts import ChatPromptTemplate

prompt=ChatPromptTemplate.from_messages(
    [
        ("system","You are an expert AI Engineer.<response><answer>Your answer here</answer></response>.Provide me answer based on the question"),
        ("user","{input}")
    ]
)
prompt

### OutputParser
from langchain_core.output_parsers import XMLOutputParser
from langchain_core.prompts import PromptTemplate

output_parser=XMLOutputParser()

prompt=PromptTemplate(
    template="Answer the user query \n {format_instruction}\n {query}\n ",
    input_variables=["query"],
    partial_variables={"format_instruction":output_parser.get_format_instructions()},
)
prompt

chain=prompt|model
response=chain.invoke({"query":"Can you tell me about Langsmith?"})
print(response)

##output parser
#from langchain_core.output_parsers import XMLOutputParser
from langchain.output_parsers.xml import XMLOutputParser

# XML Output Parser
output_parser = XMLOutputParser()

# Prompt that instructs the model to return XML
prompt = ChatPromptTemplate.from_messages([
    ("system", "You are a helpful assistant. Respond in this XML format: <response><answer>Your answer here</answer></response>"),
    ("human", "{input}")
])

# Build the chain
chain = prompt | model

# Run the chain
#response = chain.invoke({"input": "What is LangChain?"})

raw_output =chain.invoke({"input": "What is LangChain?"})

# Print result
print(raw_output)


## With Pydantic
from langchain_core.output_parsers import JsonOutputParser
from langchain_core.prompts import PromptTemplate
from langchain_openai import ChatOpenAI
from pydantic import BaseModel, Field

model = ChatOpenAI(temperature=0.7)


# Define your desired data structure.
class Joke(BaseModel):
    setup: str = Field(description="question to set up a joke")
    punchline: str = Field(description="answer to resolve the joke")


# And a query intented to prompt a language model to populate the data structure.
joke_query = "Tell me a joke."

# Set up a parser + inject instructions into the prompt template.
parser = JsonOutputParser(pydantic_object=Joke)

prompt = PromptTemplate(
    template="Answer the user query.\n{format_instructions}\n{query}\n",
    input_variables=["query"],
    partial_variables={"format_instructions": parser.get_format_instructions()},
)

chain = prompt | model | parser

chain.invoke({"query": joke_query})

### Without Pydantic
joke_query = "Tell me a joke ."

parser = JsonOutputParser()

prompt = PromptTemplate(
    template="Answer the user query.\n{format_instructions}\n{query}\n",
    input_variables=["query"],
    partial_variables={"format_instructions": parser.get_format_instructions()},
)

chain = prompt | model | parser

chain.invoke({"query": joke_query})


from langchain_core.output_parsers import XMLOutputParser
from langchain_core.prompts import PromptTemplate


actor_query = "Generate the shortened filmography for Tom Hanks."

output = model.invoke(
    f"""{actor_query}
Please enclose the movies in <movie></movie> tags"""
)

print(output.content)

from langchain.output_parsers import YamlOutputParser
from langchain_core.prompts import PromptTemplate
from langchain_openai import ChatOpenAI
from pydantic import BaseModel, Field


# Define your desired data structure.
class Joke(BaseModel):
    setup: str = Field(description="question to set up a joke")
    punchline: str = Field(description="answer to resolve the joke")


model = ChatOpenAI(temperature=0.5)

# And a query intented to prompt a language model to populate the data structure.
joke_query = "Tell me a joke."

# Set up a parser + inject instructions into the prompt template.
parser = YamlOutputParser(pydantic_object=Joke)

prompt = PromptTemplate(
    template="Answer the user query.\n{format_instructions}\n{query}\n",
    input_variables=["query"],
    partial_variables={"format_instructions": parser.get_format_instructions()},
)

chain = prompt | model | parser

chain.invoke({"query": joke_query})