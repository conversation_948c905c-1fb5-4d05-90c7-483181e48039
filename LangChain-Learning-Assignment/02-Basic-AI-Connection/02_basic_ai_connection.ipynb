# Step 1: Import everything we need
import os
from dotenv import load_dotenv
from langchain_openai import ChatOpenAI
from langchain_groq import ChatGroq

# Load our API keys
load_dotenv()

# Set environment variables for this session
os.environ["OPENAI_API_KEY"] = os.getenv("OPENAI_API_KEY")
os.environ["GROQ_API_KEY"] = os.getenv("GROQ_API_KEY")
os.environ["LANGCHAIN_API_KEY"] = os.getenv("LANGCHAIN_API_KEY")
os.environ["LANGCHAIN_PROJECT"] = os.getenv("LANGCHAIN_PROJECT")
os.environ["LANGCHAIN_TRACING_V2"] = "true"


print("✅ Environment ready!")
print("📊 LangChain tracing enabled - you can monitor your AI calls")

# Step 2: Create your first AI model connection
# Think of this like getting a phone number for a smart assistant

openai_model = ChatOpenAI(
    model="gpt-3.5-turbo",  # Which AI brain to use
    temperature=0.7        # How creative should it be? (0 = boring, 1 = very creative)
)

print("✅ OpenAI model created!")
print(f"📱 Model name: {openai_model.model_name}")
print(f"🌡️ Temperature: {openai_model.temperature}")
print(f"🔑 Has API key: {'Yes' if openai_model.openai_api_key else 'No'}")

# Step 3: Have your first conversation with AI
# This is like making your first phone call!

question = "What is artificial intelligence in simple terms?"
print(f"🙋 You asked: {question}")
print("⏳ Waiting for AI response...")

# Send the question to AI and get response
response = openai_model.invoke(question)

print("\n🤖 AI Response:")
print(response.content)
print(f"\n📊 Response length: {len(response.content)} characters")

# Step 4: Explore what's inside an AI response
# Think of this like examining a letter you received

print("🔍 Let's examine the AI response object:")
print(f"📝 Type: {type(response)}")
print(f"📄 Content: {response.content[:100]}...")  # First 100 characters
print(f"🏷️ Response metadata: {response.response_metadata}")

# The most important part is usually the content
print("\n💡 The main text is in: response.content")
print("💡 This is what you'll use most of the time!")