{"cells": [{"cell_type": "markdown", "id": "beff34fb", "metadata": {}, "source": ["### Data Ingestion or Data Loader\n", "https://python.langchain.com/docs/integrations/document_loaders/"]}, {"cell_type": "code", "execution_count": 15, "id": "4c5285f6", "metadata": {}, "outputs": [], "source": ["### text loader\n", "from langchain_community.document_loaders.text import TextLoader"]}, {"cell_type": "code", "execution_count": 16, "id": "ea38a204", "metadata": {}, "outputs": [{"data": {"text/plain": ["<langchain_community.document_loaders.text.TextLoader at 0x10eb31840>"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["loader=TextLoader('speech.txt')\n", "loader"]}, {"cell_type": "code", "execution_count": 17, "id": "4d472cb0", "metadata": {}, "outputs": [{"data": {"text/plain": ["[Document(metadata={'source': 'speech.txt'}, page_content='The world must be made safe for democracy. Its peace must be planted upon the tested foundations of political liberty. We have no selfish ends to serve. We desire no conquest, no dominion. We seek no indemnities for ourselves, no material compensation for the sacrifices we shall freely make. We are but one of the champions of the rights of mankind. We shall be satisfied when those rights have been made as secure as the faith and the freedom of nations can make them.\\n\\nJust because we fight without rancor and without selfish object, seeking nothing for ourselves but what we shall wish to share with all free peoples, we shall, I feel confident, conduct our operations as belligerents without passion and ourselves observe with proud punctilio the principles of right and of fair play we profess to be fighting for.\\n\\n…\\n\\nIt will be all the easier for us to conduct ourselves as belligerents in a high spirit of right and fairness because we act without animus, not in enmity toward a people or with the desire to bring any injury or disadvantage upon them, but only in armed opposition to an irresponsible government which has thrown aside all considerations of humanity and of right and is running amuck. We are, let me say again, the sincere friends of the German people, and shall desire nothing so much as the early reestablishment of intimate relations of mutual advantage between us—however hard it may be for them, for the time being, to believe that this is spoken from our hearts.\\n\\nWe have borne with their present government through all these bitter months because of that friendship—exercising a patience and forbearance which would otherwise have been impossible. We shall, happily, still have an opportunity to prove that friendship in our daily attitude and actions toward the millions of men and women of German birth and native sympathy who live among us and share our life, and we shall be proud to prove it toward all who are in fact loyal to their neighbors and to the government in the hour of test. They are, most of them, as true and loyal Americans as if they had never known any other fealty or allegiance. They will be prompt to stand with us in rebuking and restraining the few who may be of a different mind and purpose. If there should be disloyalty, it will be dealt with with a firm hand of stern repression; but, if it lifts its head at all, it will lift it only here and there and without countenance except from a lawless and malignant few.\\n\\nIt is a distressing and oppressive duty, gentlemen of the Congress, which I have performed in thus addressing you. There are, it may be, many months of fiery trial and sacrifice ahead of us. It is a fearful thing to lead this great peaceful people into war, into the most terrible and disastrous of all wars, civilization itself seeming to be in the balance. But the right is more precious than peace, and we shall fight for the things which we have always carried nearest our hearts—for democracy, for the right of those who submit to authority to have a voice in their own governments, for the rights and liberties of small nations, for a universal dominion of right by such a concert of free peoples as shall bring peace and safety to all nations and make the world itself at last free.\\n\\nTo such a task we can dedicate our lives and our fortunes, everything that we are and everything that we have, with the pride of those who know that the day has come when America is privileged to spend her blood and her might for the principles that gave her birth and happiness and the peace which she has treasured. God helping her, she can do no other.')]"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["text_documents=loader.load()\n", "text_documents"]}, {"cell_type": "code", "execution_count": 18, "id": "32d11c53", "metadata": {}, "outputs": [{"data": {"text/plain": ["[Document(metadata={'producer': '<PERSON><PERSON>', 'creator': '<PERSON><PERSON>', 'creationdate': '2025-01-30T20:27:03+00:00', 'title': 'Ultimate Data Science & GenAI Bootcamp', 'moddate': '2025-01-30T20:26:59+00:00', 'keywords': 'DAGdmhcqnYw,BAEmsmap8Lg,0', 'author': 'monal singh', 'containsaigeneratedcontent': 'Yes', 'source': 'syllabus.pdf', 'total_pages': 34, 'page': 0, 'page_label': '1'}, page_content='MACHINE\\nLEARNING\\nDEEP\\nLEARNING\\nPYTHON +\\nSTATS\\nCOMPUTER VISIONNATURAL LANGUAGE PROCESSING\\nGENERATIVE AI\\nRETRIEVAL AUGUMENT GENERATION\\nVECTOR DB'),\n", " Document(metadata={'producer': '<PERSON><PERSON>', 'creator': '<PERSON><PERSON>', 'creationdate': '2025-01-30T20:27:03+00:00', 'title': 'Ultimate Data Science & GenAI Bootcamp', 'moddate': '2025-01-30T20:26:59+00:00', 'keywords': 'DAGdmhcqnYw,BAEmsmap8Lg,0', 'author': 'monal singh', 'containsaigeneratedcontent': 'Yes', 'source': 'syllabus.pdf', 'total_pages': 34, 'page': 1, 'page_label': '2'}, page_content='This course is designed for aspiring data scientists, machine learning enthusiasts, and\\nprofessionals looking to build expertise in Python programming, data analysis, machine learning,\\nand deep learning. Whether you are just starting or have some experience, this comprehensive\\ncourse will equip you with the skills needed to work with real-world datasets, apply machine\\nlearning algorithms, and deploy AI solutions. By the end of the course, you’ll have a solid\\nfoundation in AI, a portfolio of end-to-end projects, and the confidence to tackle complex\\nchallenges in data science and AI.\\nLearning Objectives\\nMaster Python Programming: Understand Python fundamentals, including data types,\\ncontrol structures, and object-oriented programming, to write efficient and reusable\\ncode.\\nHandle Data with Pandas and NumPy: Acquire skills to manipulate, clean, and\\npreprocess large datasets using Pandas and NumPy for data analysis tasks.\\nVisualize Data: Create compelling data visualizations using libraries such as Matplotlib,\\nSeaborn, and Plotly to present insights effectively.\\nUnderstand SQL & NoSQL: Gain expertise in both relational (SQL) and non-relational\\n(NoSQL) databases, including MongoDB, for storing, querying, and managing data.\\nGrasp Statistics and Probability: Understand the core concepts of statistics,\\nprobability, and hypothesis testing, applying them to data analysis and machine\\nlearning.\\nMaster Machine Learning Techniques: Learn key machine learning algorithms,\\nincluding supervised, unsupervised, and ensemble methods, and apply them to real-\\nworld problems.\\nDive into Deep Learning: Develop a strong understanding of neural networks, CNNs,\\nRNNs, and transformers, with hands-on implementation for advanced AI tasks.\\nExplore Generative AI & Vector Databases: Learn the concepts and applications of\\ngenerative models, vector databases, and retrieval-augmented generation to handle\\ncomplex AI systems.\\nBuild Real-World Projects: Implement end-to-end machine learning and AI projects,\\nfrom data preprocessing to model deployment, integrating concepts from multiple\\nmodules.\\nUltimate Data Science & GenAI Bootcamp       Page  2'),\n", " Document(metadata={'producer': '<PERSON><PERSON>', 'creator': '<PERSON><PERSON>', 'creationdate': '2025-01-30T20:27:03+00:00', 'title': 'Ultimate Data Science & GenAI Bootcamp', 'moddate': '2025-01-30T20:26:59+00:00', 'keywords': 'DAGdmhcqnYw,BAEmsmap8Lg,0', 'author': 'monal singh', 'containsaigeneratedcontent': 'Yes', 'source': 'syllabus.pdf', 'total_pages': 34, 'page': 2, 'page_label': '3'}, page_content=\"Course Information\\nNo prerequisites are required for this course. The curriculum covers everything from the\\nbasics of Python programming, statistics, and machine learning to advanced topics in deep\\nlearning, NLP, and generative AI. Whether you're a beginner or have some prior experience,\\nthe course will ensure you gain the skills needed to succeed.\\nEstimated Time\\n8 months 6hrs/week*\\nRequired Skill Level\\nBegineer\\nThe course is designed to be completed over a duration of approximately 7 to 8 months, providing\\nan in-depth exploration from Python basics to GenAI, with plenty of time for practical\\nimplementation and real-world applications.\\nPrerequisites\\nUltimate Data Science & GenAI Bootcamp       Page  3\"),\n", " Document(metadata={'producer': '<PERSON><PERSON>', 'creator': '<PERSON><PERSON>', 'creationdate': '2025-01-30T20:27:03+00:00', 'title': 'Ultimate Data Science & GenAI Bootcamp', 'moddate': '2025-01-30T20:26:59+00:00', 'keywords': 'DAGdmhcqnYw,BAEmsmap8Lg,0', 'author': 'monal singh', 'containsaigeneratedcontent': 'Yes', 'source': 'syllabus.pdf', 'total_pages': 34, 'page': 3, 'page_label': '4'}, page_content='Course Instructors\\nSunny SavitaGenAI Engineer\\nLinkedin\\nKrish NaikChief AI Engineer\\nLinkedin\\nUltimate Data Science & GenAI Bootcamp       Page  4\\nSourangshu PalSenior Data Scientist\\nLinkedin\\nMonal KumarData Scientist\\nLinkedin\\nMayank AggrawalSenior ML Engineer\\nLinkedin\\nDarius B.Head of Product\\nLinkedin'),\n", " Document(metadata={'producer': '<PERSON><PERSON>', 'creator': '<PERSON><PERSON>', 'creationdate': '2025-01-30T20:27:03+00:00', 'title': 'Ultimate Data Science & GenAI Bootcamp', 'moddate': '2025-01-30T20:26:59+00:00', 'keywords': 'DAGdmhcqnYw,BAEmsmap8Lg,0', 'author': 'monal singh', 'containsaigeneratedcontent': 'Yes', 'source': 'syllabus.pdf', 'total_pages': 34, 'page': 4, 'page_label': '5'}, page_content=\"In this module, you’ll get a solid introduction to Python, covering essential programming concepts\\nsuch as variables, data types, operators, and control flow. You’ll learn how to manipulate strings,\\nlists, dictionaries, and other basic data structures. The module will also guide you through writing\\nsimple functions and using loops and conditionals effectively. By the end, you'll have a strong\\nunderstanding of Python syntax, preparing you to tackle more complex programming challenges\\nand form a foundation for learning advanced concepts.\\nUltimate Data Science & GenAI Bootcamp       Page  5\\nPython Foundations\\nModule 1\\nTopics\\nIntroduction to Python Comparison with other programming\\nlanguages, Python objects: Numbers,\\nBooleans, and Strings\\nData Structures & Operations Container objects and mutability,\\nOperators, Operator precedence and\\nassociativity\\nControl Flow Conditional statements, Loops, break\\nand continue statements\\nString Manipulation Basics of string objects, Inbuilt string\\nmethods, Splitting and joining strings,\\nString formatting functions\\nLists & Collections List methods, list comprehension, Lists as\\nstacks and queues, Tuples, sets, and\\ndictionaries, Dictionary comprehensions\\nand view objects\"),\n", " Document(metadata={'producer': '<PERSON><PERSON>', 'creator': '<PERSON><PERSON>', 'creationdate': '2025-01-30T20:27:03+00:00', 'title': 'Ultimate Data Science & GenAI Bootcamp', 'moddate': '2025-01-30T20:26:59+00:00', 'keywords': 'DAGdmhcqnYw,BAEmsmap8Lg,0', 'author': 'monal singh', 'containsaigeneratedcontent': 'Yes', 'source': 'syllabus.pdf', 'total_pages': 34, 'page': 5, 'page_label': '6'}, page_content='Topics\\nFunctions & Iterators Function basics and parameter passing,\\nIterators and generator functions,\\nLambda functions, map(), reduce(),\\nfilter()\\nPython Foundations\\nModule 1\\nUltimate Data Science & GenAI Bootcamp       Page  6'),\n", " Document(metadata={'producer': '<PERSON><PERSON>', 'creator': '<PERSON><PERSON>', 'creationdate': '2025-01-30T20:27:03+00:00', 'title': 'Ultimate Data Science & GenAI Bootcamp', 'moddate': '2025-01-30T20:26:59+00:00', 'keywords': 'DAGdmhcqnYw,BAEmsmap8Lg,0', 'author': 'monal singh', 'containsaigeneratedcontent': 'Yes', 'source': 'syllabus.pdf', 'total_pages': 34, 'page': 6, 'page_label': '7'}, page_content='This module takes your Python skills further by diving into object-oriented programming (OOP)\\nconcepts like classes, inheritance, and polymorphism. You’ll also explore more advanced topics\\nsuch as decorators, lambda functions, iterators, and generator functions. Additionally, we cover\\nexception handling, file operations, and working with modules and libraries. By the end, you will be\\ncomfortable building more sophisticated Python applications and writing efficient, reusable code.\\nAdvanced Python Programming\\nModule 2\\nTopics\\nObject-Oriented Programming (OOP) OOP basics and class creation,\\nInheritance, Polymorphism,\\nEncapsulation, and Abstraction,\\nDecorators, class methods, and static\\nmethods, Special (Magic/Dunder)\\nmethods, Property decorators: Getters,\\nsetters, and delete methods\\nFile Handling & Logging Reading and writing files, Buffered read\\nand write operations, more file methods,\\nLogging and debugging\\nModules & Exception Handling Importing modules and using them\\neffectively, Exception handling\\nConcurrency & Parallelism Introduction to multithreading,\\nMultiprocessing for performance\\noptimization\\nUltimate Data Science & GenAI Bootcamp       Page  7'),\n", " Document(metadata={'producer': '<PERSON><PERSON>', 'creator': '<PERSON><PERSON>', 'creationdate': '2025-01-30T20:27:03+00:00', 'title': 'Ultimate Data Science & GenAI Bootcamp', 'moddate': '2025-01-30T20:26:59+00:00', 'keywords': 'DAGdmhcqnYw,BAEmsmap8Lg,0', 'author': 'monal singh', 'containsaigeneratedcontent': 'Yes', 'source': 'syllabus.pdf', 'total_pages': 34, 'page': 7, 'page_label': '8'}, page_content='In this module, you will master the core aspects of data manipulation using Pandas. You’ll learn\\nhow to work with Series, DataFrames, and Panels, as well as perform data selection, filtering, and\\nsorting. The module covers critical tasks like handling missing data, reindexing, and applying\\nstatistical functions to datasets. You’ll also gain hands-on experience with data visualization and\\nadvanced indexing techniques, empowering you to efficiently analyze and manipulate complex\\ndatasets.\\nMastering Data Handling with Pandas\\nTopics\\nData Structures & Fundamentals Series, DataFrame, Panel, Basic\\nFunctionality, Indexing & Selecting, Re-\\nindexing, Iteration\\nData Operations & Transformations Sorting, Working with Text Data, Options\\n& Customization, Categorical Data, Date\\nFunctionality, Time Delta\\nData Analysis & Statistical Functions Data Statistical Functions, Window\\nFunctions\\nReading, Writing & Visualization Reading Data from Different File\\nSystems, Visualization, Tools\\nUltimate Data Science & GenAI Bootcamp       Page  8\\nModule 3'),\n", " Document(metadata={'producer': '<PERSON><PERSON>', 'creator': '<PERSON><PERSON>', 'creationdate': '2025-01-30T20:27:03+00:00', 'title': 'Ultimate Data Science & GenAI Bootcamp', 'moddate': '2025-01-30T20:26:59+00:00', 'keywords': 'DAGdmhcqnYw,BAEmsmap8Lg,0', 'author': 'monal singh', 'containsaigeneratedcontent': 'Yes', 'source': 'syllabus.pdf', 'total_pages': 34, 'page': 8, 'page_label': '9'}, page_content='This module introduces you to NumPy, a key library for numerical computing in Python. You’ll learn\\nhow to create and manipulate NumPy arrays, perform advanced indexing, and understand\\nbroadcasting. The module covers essential mathematical and statistical functions, including array\\nmanipulations, binary operations, and vectorized operations. By the end, you’ll have the skills to\\nefficiently perform complex numerical computations and leverage NumPy for machine learning\\nand deep learning applications.\\nMastering NumPy\\nTopics\\nNumPy Basics & Array Creation NdArray Object, Data Types, Array\\nAttributes, Array Creation Routines,\\nArray from Existing Data, Data Array from\\nNumerical Ranges\\nIndexing, Slicing & Advanced Indexing Indexing & Slicing, Advanced Indexing\\nArray Operations & Manipulation Array Manipulation, Binary Operators,\\nString Functions, Arithmetic Operations,\\nMathematical Functions\\nMathematical & Statistical Analysis Statistical Functions, Sort, Search &\\nCounting Functions, Matrix Library,\\nLinear Algebra\\nAdvanced Concepts Broadcasting, Iterating Over Array, Byte\\nSwapping, Copies & Views\\nUltimate Data Science & GenAI Bootcamp       Page  9\\nModule 4'),\n", " Document(metadata={'producer': '<PERSON><PERSON>', 'creator': '<PERSON><PERSON>', 'creationdate': '2025-01-30T20:27:03+00:00', 'title': 'Ultimate Data Science & GenAI Bootcamp', 'moddate': '2025-01-30T20:26:59+00:00', 'keywords': 'DAGdmhcqnYw,BAEmsmap8Lg,0', 'author': 'monal singh', 'containsaigeneratedcontent': 'Yes', 'source': 'syllabus.pdf', 'total_pages': 34, 'page': 9, 'page_label': '10'}, page_content=\"In this module, you’ll learn how to visualize data effectively using Python's popular libraries,\\nMatplotlib, Seaborn, and Plotly. You’ll cover essential plot types like line charts, bar graphs, and\\nscatter plots, and learn how to customize these visualizations to highlight key insights. Additionally,\\nthe module teaches you how to visualize statistical data, correlations, and distributions, helping\\nyou communicate data-driven findings in a visually compelling way.\\nData Visualization with Python\\nTopics\\nIntroduction to Data Visualization Overview of Data Visualization, Principles\\nof Good Visualization\\nMatplotlib Introduction to <PERSON><PERSON><PERSON>li<PERSON>, Creating\\nBasic Plots (<PERSON>, <PERSON>, <PERSON>atter),\\nCustomizing Axes, Titles, Legends, and\\nLabels, Working with Subplots, Saving\\nand Exporting Figures\\nSeaborn Introduction to Seaborn, Visualizing\\nDistributions, Relationship Plots\\n(Pairplots, Heatmaps), Categorical Data\\nVisualization, Advanced Plot\\nCustomizations\\nPlotly Introduction to Plotly, Creating\\nInteractive Plots (Line, Bar, Scatter),\\nCustomizing Plots, Dashboards and\\nInteractive Layouts, Plotly Express\\nUltimate Data Science & GenAI Bootcamp       Page  10\\nModule 5\"),\n", " Document(metadata={'producer': '<PERSON><PERSON>', 'creator': '<PERSON><PERSON>', 'creationdate': '2025-01-30T20:27:03+00:00', 'title': 'Ultimate Data Science & GenAI Bootcamp', 'moddate': '2025-01-30T20:26:59+00:00', 'keywords': 'DAGdmhcqnYw,BAEmsmap8Lg,0', 'author': 'monal singh', 'containsaigeneratedcontent': 'Yes', 'source': 'syllabus.pdf', 'total_pages': 34, 'page': 10, 'page_label': '11'}, page_content='This module dives into advanced SQL techniques, including complex queries, joins, and indexing\\nfor efficient data retrieval. You’ll learn how to implement stored procedures, triggers, and\\nfunctions, and explore the use of window functions and partitions. The module covers key\\ndatabase design concepts like primary and foreign keys and normalization. By the end, you’ll be\\nproficient in managing large-scale databases and optimizing SQL queries for performance.\\nAdvanced SQL and Database Management\\nTopics\\nIntroduction to SQL Introduction to SQL, SQL Queries:\\nSELECT, INSERT, UPDATE, DELETE\\nSQL Functions and Procedures SQL Functions (Aggregate, Scalar),\\nStored Procedures, User-defined\\nFunctions (UDFs), Function and\\nProcedure Syntax\\nDatabase Constraints Primary and Foreign Keys, Data Integrity,\\nReferential Integrity\\nAdvanced SQL Techniques Window Functions, Partitioning, CTE\\n(Common Table Expressions), Indexing\\nSQL Joins and Unions Inner Join, Left Join, Right Join, Full Outer\\nJoin, Cross Join, Union\\nTriggers and Case Statements Triggers (Before, After), CASE\\nStatements, Conditional Logic\\nUltimate Data Science & GenAI Bootcamp       Page  11\\nModule 6'),\n", " Document(metadata={'producer': '<PERSON><PERSON>', 'creator': '<PERSON><PERSON>', 'creationdate': '2025-01-30T20:27:03+00:00', 'title': 'Ultimate Data Science & GenAI Bootcamp', 'moddate': '2025-01-30T20:26:59+00:00', 'keywords': 'DAGdmhcqnYw,BAEmsmap8Lg,0', 'author': 'monal singh', 'containsaigeneratedcontent': 'Yes', 'source': 'syllabus.pdf', 'total_pages': 34, 'page': 11, 'page_label': '12'}, page_content='Advanced SQL and Database Management\\nTopics\\nNormalization and Pivoting Normalization Forms (1NF, 2NF, 3NF),\\nPivot Tables, Data Aggregation\\nUltimate Data Science & GenAI Bootcamp       Page  12\\nModule 6'),\n", " Document(metadata={'producer': '<PERSON><PERSON>', 'creator': '<PERSON><PERSON>', 'creationdate': '2025-01-30T20:27:03+00:00', 'title': 'Ultimate Data Science & GenAI Bootcamp', 'moddate': '2025-01-30T20:26:59+00:00', 'keywords': 'DAGdmhcqnYw,BAEmsmap8Lg,0', 'author': 'monal singh', 'containsaigeneratedcontent': 'Yes', 'source': 'syllabus.pdf', 'total_pages': 34, 'page': 12, 'page_label': '13'}, page_content=\"In this module, you'll explore the world of NoSQL databases with MongoDB. You'll learn how to\\ncreate and manage databases, collections, and documents, and perform CRUD operations. The\\nmodule covers querying, sorting, and indexing, providing a comprehensive understanding of\\nMongoDB's flexible data model. By the end, you’ll be able to efficiently work with NoSQL\\ndatabases, particularly for use cases that involve unstructured or semi-structured data.\\nIntroduction to NoSQL with MongoDB\\nTopics\\nGetting Started with MongoDB MongoDB Introduction, Setting up\\nMongoDB, MongoDB Shell Commands\\nDatabase and Collection Management MongoDB Create Database, MongoDB\\nCreate Collection\\nCRUD Operations MongoDB Insert, MongoDB Find,\\nMongoDB Update, MongoDB Delete\\nQuerying MongoDB MongoDB Query, MongoDB Sort,\\nMongoDB Limit\\nManaging Collections MongoDB Drop Collection, MongoDB\\nDelete (Specific)\\nUltimate Data Science & GenAI Bootcamp       Page  13\\nModule 7\"),\n", " Document(metadata={'producer': '<PERSON><PERSON>', 'creator': '<PERSON><PERSON>', 'creationdate': '2025-01-30T20:27:03+00:00', 'title': 'Ultimate Data Science & GenAI Bootcamp', 'moddate': '2025-01-30T20:26:59+00:00', 'keywords': 'DAGdmhcqnYw,BAEmsmap8Lg,0', 'author': 'monal singh', 'containsaigeneratedcontent': 'Yes', 'source': 'syllabus.pdf', 'total_pages': 34, 'page': 13, 'page_label': '14'}, page_content='This module provides a foundation in statistics and probability, covering essential terms, concepts,\\nand methods. You’ll learn about different types of data, levels of measurement, and key statistical\\nmeasures like mean, median, variance, and standard deviation. The module introduces random\\nvariables, probability distributions, and various types of probability functions, giving you a strong\\nbase to analyze and interpret data from a statistical perspective.\\nFoundations of Statistics and Probability\\nTopics\\nIntroduction to Statistics Introduction to Basic Statistics Terms,\\nTypes of Statistics, Types of Data, Levels\\nof Measurement, Measures of Central\\nTendency, Measures of Dispersion\\nExploring Random Variables and\\nProbability\\nRandom Variables, Set Theory,\\nSkewness, Covariance and Correlation,\\nProbability Density/Distribution Function\\nDistributions and Their Applications Types of Probability Distributions,\\nBinomial Distribution, Poisson\\nDistribution, Normal Distribution\\n(Gaussian Distribution), Probability\\nDensity Function and Mass Function,\\nCumulative Density Function, Examples\\nof Normal Distribution, Bernoulli\\nDistribution, Uniform Distribution\\nStatistical Inference Z-Statistics, Central Limit Theorem,\\nEstimation, Hypothesis Testing\\nUltimate Data Science & GenAI Bootcamp       Page  14\\nModule 8'),\n", " Document(metadata={'producer': '<PERSON><PERSON>', 'creator': '<PERSON><PERSON>', 'creationdate': '2025-01-30T20:27:03+00:00', 'title': 'Ultimate Data Science & GenAI Bootcamp', 'moddate': '2025-01-30T20:26:59+00:00', 'keywords': 'DAGdmhcqnYw,BAEmsmap8Lg,0', 'author': 'monal singh', 'containsaigeneratedcontent': 'Yes', 'source': 'syllabus.pdf', 'total_pages': 34, 'page': 14, 'page_label': '15'}, page_content=\"In this module, you'll delve deeper into statistical inference techniques, including hypothesis\\ntesting, confidence intervals, and the types of errors in statistical tests. You’ll explore advanced\\nconcepts like P-values, T-tests, and Chi-square tests, learning how to interpret results in the\\ncontext of real-world data. By the end, you’ll be equipped to conduct sophisticated statistical\\nanalysis and make informed decisions based on data-driven evidence.\\nAdvanced Statistical Inference and\\nHypothesis Testing\\nTopics\\nHypothesis Testing and Errors Hypothesis Testing Mechanism, Type 1 &\\nType 2 Error, T-Tests vs. Z-Tests:\\nOverview, When to Use a T-Test vs. Z-\\nTest\\nStatistical Distributions and Tests T-Stats, Student T Distribution, Chi-\\nSquare Test, Chi-Square Distribution\\nUsing Python, Chi-Square for Goodness\\nof Fit Test\\nBayesian Statistics and Confidence\\nIntervals\\nBayes Statistics (Bayes Theorem),\\nConfidence Interval (CI), Confidence\\nIntervals and the Margin of Error,\\nInterpreting Confidence Levels and\\nConfidence Intervals\\nStatistical Significance and\\nInterpretation\\nP-Value, T-Stats vs. Z-Stats: Overview\\nUltimate Data Science & GenAI Bootcamp       Page  15\\nModule 9\"),\n", " Document(metadata={'producer': '<PERSON><PERSON>', 'creator': '<PERSON><PERSON>', 'creationdate': '2025-01-30T20:27:03+00:00', 'title': 'Ultimate Data Science & GenAI Bootcamp', 'moddate': '2025-01-30T20:26:59+00:00', 'keywords': 'DAGdmhcqnYw,BAEmsmap8Lg,0', 'author': 'monal singh', 'containsaigeneratedcontent': 'Yes', 'source': 'syllabus.pdf', 'total_pages': 34, 'page': 15, 'page_label': '16'}, page_content='This module covers essential techniques for preparing and transforming data before applying\\nmachine learning models. You’ll learn how to handle missing values, deal with imbalanced data,\\nand scale or encode features. The module also explores methods for handling outliers, feature\\nselection (including forward/backward elimination), and dimensionality reduction techniques. By\\nthe end, you’ll be proficient in preparing high-quality datasets that are ready for modeling.\\nFeature Engineering and Data\\nPreprocessing\\nTopics\\nHandling Missing and Imbalanced\\nData\\nHandling Missing Data, Handling\\nImbalanced Data\\nOutliers and Scaling Handling Outliers, Feature Scaling\\nData Transformation and Encoding Data Encoding\\nFeature Selection Techniques Backward Elimination, Forward\\nElimination, Recursive Feature\\nElimination\\nCorrelation and Multicollinearity Covariance and Correlation, VIF\\nUltimate Data Science & GenAI Bootcamp       Page  16\\nModule 10'),\n", " Document(metadata={'producer': '<PERSON><PERSON>', 'creator': '<PERSON><PERSON>', 'creationdate': '2025-01-30T20:27:03+00:00', 'title': 'Ultimate Data Science & GenAI Bootcamp', 'moddate': '2025-01-30T20:26:59+00:00', 'keywords': 'DAGdmhcqnYw,BAEmsmap8Lg,0', 'author': 'monal singh', 'containsaigeneratedcontent': 'Yes', 'source': 'syllabus.pdf', 'total_pages': 34, 'page': 16, 'page_label': '17'}, page_content='In this module, you’ll learn how to perform Exploratory Data Analysis (EDA) to uncover patterns,\\ntrends, and relationships in your data. You’ll master techniques for visualizing distributions,\\nidentifying correlations, and detecting anomalies. The module emphasizes the importance of\\nsummary statistics, data cleaning, and feature engineering. By the end, you’ll be able to extract\\nmeaningful insights from raw data and prepare it for further analysis or modeling.\\nExploratory Data Analysis (EDA) for\\nDetailed Insights\\nTopics\\nTrend Analysis and Segmentation Analyzing Bike Sharing Trends,\\nCustomer Segmentation and Effective\\nCross-Selling\\nSentiment and Quality Analysis Analyzing Movie Reviews Sentiment,\\nAnalyzing Wine Types and Quality\\nRecommendation and Forecasting Analyzing Music Trends and\\nRecommendations, Forecasting Stock\\nand Commodity Prices\\nUltimate Data Science & GenAI Bootcamp       Page  17\\nModule 11'),\n", " Document(metadata={'producer': '<PERSON><PERSON>', 'creator': '<PERSON><PERSON>', 'creationdate': '2025-01-30T20:27:03+00:00', 'title': 'Ultimate Data Science & GenAI Bootcamp', 'moddate': '2025-01-30T20:26:59+00:00', 'keywords': 'DAGdmhcqnYw,BAEmsmap8Lg,0', 'author': 'monal singh', 'containsaigeneratedcontent': 'Yes', 'source': 'syllabus.pdf', 'total_pages': 34, 'page': 17, 'page_label': '18'}, page_content='This module provides a comprehensive introduction to machine learning, covering key algorithms\\nand techniques. You’ll learn the differences between supervised and unsupervised learning, as\\nwell as the core concepts of regression, classification, and clustering. The module introduces\\nmodel evaluation metrics like accuracy, precision, recall, and F1-score, giving you the foundation to\\nunderstand and implement machine learning models in real-world scenarios.\\nMachine Learning Foundations and\\nTechniques\\nTopics\\nIntroduction to Machine Learning AI vs ML vs DL vs DS, Types of ML\\nTechniques, Supervised vs Unsupervised\\nvs Semi-Supervised vs Reinforcement\\nLearning\\nLinear Regression Simple Linear Regression, Multiple Linear\\nRegression, MSE, MAE, RMSE, R-\\nsquared, Adjusted R-squared, Linear\\nRegression with OLS\\nRegularization Techniques Ridge Regression, Lasso Regression,\\nElasticNet\\nLogistic Regression Logistic Regression, Performance\\nMetrics: Confusion Matrix, Accuracy,\\nPrecision, Recall, F-Beta Score, ROC-\\nAUC Curve\\nSupport Vector Machines (SVM) Support Vector Classifiers, Support\\nVector Regressor, Support Vector\\nKernels\\nUltimate Data Science & GenAI Bootcamp       Page  18\\nModule 12'),\n", " Document(metadata={'producer': '<PERSON><PERSON>', 'creator': 'Canva', 'creationdate': '2025-01-30T20:27:03+00:00', 'title': 'Ultimate Data Science & GenAI Bootcamp', 'moddate': '2025-01-30T20:26:59+00:00', 'keywords': 'DAGdmhcqnYw,BAEmsmap8Lg,0', 'author': 'monal singh', 'containsaigeneratedcontent': 'Yes', 'source': 'syllabus.pdf', 'total_pages': 34, 'page': 18, 'page_label': '19'}, page_content='Machine Learning Foundations and\\nTechniques\\nTopics\\nBayes Theorem and Naive Bayes Introduction to Bayes Theorem, Naive\\nBayes Classifier\\nK-Nearest Neighbors (KNN) KNN Classifier, KNN Regressor\\nDecision Trees Decision Tree Classifier, Decision Tree\\nRegressor\\nEnsemble Methods Bagging, Boosting, Random Forest\\nClassifier, Random Forest Regressor,\\nOut-of-Bag Evaluation, XGBoost\\nClassifier, XGBoost Regressor\\nSupport Vector Machines (SVM) Support Vector Classifiers, Support\\nVector Regressor, Support Vector\\nKernels\\nIntroduction to Unsupervised Learning Overview of Unsupervised Learning, Use\\nCases, and Applications\\nClustering Techniques KMeans Clustering, Hierarchical\\nClustering, DBSCAN Clustering\\nUltimate Data Science & GenAI Bootcamp       Page  19\\nModule 12'),\n", " Document(metadata={'producer': '<PERSON><PERSON>', 'creator': '<PERSON><PERSON>', 'creationdate': '2025-01-30T20:27:03+00:00', 'title': 'Ultimate Data Science & GenAI Bootcamp', 'moddate': '2025-01-30T20:26:59+00:00', 'keywords': 'DAGdmhcqnYw,BAEmsmap8Lg,0', 'author': 'monal singh', 'containsaigeneratedcontent': 'Yes', 'source': 'syllabus.pdf', 'total_pages': 34, 'page': 19, 'page_label': '20'}, page_content='Machine Learning Foundations and\\nTechniques\\nTopics\\nClustering Evaluation Silhouette Coefficient, Evaluation\\nMetrics for Clustering Algorithms\\nUltimate Data Science & GenAI Bootcamp       Page  20\\nModule 12'),\n", " Document(metadata={'producer': '<PERSON><PERSON>', 'creator': '<PERSON><PERSON>', 'creationdate': '2025-01-30T20:27:03+00:00', 'title': 'Ultimate Data Science & GenAI Bootcamp', 'moddate': '2025-01-30T20:26:59+00:00', 'keywords': 'DAGdmhcqnYw,BAEmsmap8Lg,0', 'author': 'monal singh', 'containsaigeneratedcontent': 'Yes', 'source': 'syllabus.pdf', 'total_pages': 34, 'page': 20, 'page_label': '21'}, page_content='In this module, you’ll explore the basics of Natural Language Processing (NLP) for machine\\nlearning applications. Topics include text preprocessing (stemming, lemmatization), tokenization,\\nand POS tagging. You’ll also learn how to implement key NLP techniques like Named Entity\\nRecognition, word embeddings (Word2Vec), and TF-IDF. By the end of this module, you’ll have the\\nskills to work with textual data and apply machine learning models to solve NLP tasks.\\nNatural Language Processing for\\nMachine Learning\\nTopics\\nIntroduction to NLP for ML Roadmap to Learn NLP for ML, Practical\\nUse Cases of NLP in Machine Learning\\nText Preprocessing Tokenization, Basic Terminology,\\nStemming, Lemmatization, Stopwords\\nText Representation One-Hot Encoding, N-Gram, Bag of\\nWords (BoW), TF-IDF Intuition\\nPart of Speech (POS) Tagging POS Tagging using NLTK, Understanding\\nPOS Tags\\nNamed Entity Recognition (NER) Introduction to NER, Implementing NER\\nwith NLTK\\nWord Embeddings Introduction to Word Embeddings,\\nBenefits of Using Word Embeddings in\\nML\\nUltimate Data Science & GenAI Bootcamp       Page  21\\nModule 13'),\n", " Document(metadata={'producer': '<PERSON><PERSON>', 'creator': '<PERSON><PERSON>', 'creationdate': '2025-01-30T20:27:03+00:00', 'title': 'Ultimate Data Science & GenAI Bootcamp', 'moddate': '2025-01-30T20:26:59+00:00', 'keywords': 'DAGdmhcqnYw,BAEmsmap8Lg,0', 'author': 'monal singh', 'containsaigeneratedcontent': 'Yes', 'source': 'syllabus.pdf', 'total_pages': 34, 'page': 21, 'page_label': '22'}, page_content='Natural Language Processing for\\nMachine Learning\\nTopics\\nWord2Vec Intuition behind Word2Vec, Training\\nWord2Vec Models, Skip-gram and\\nCBOW Architectures\\nUltimate Data Science & GenAI Bootcamp       Page  22\\nModule 13'),\n", " Document(metadata={'producer': '<PERSON><PERSON>', 'creator': '<PERSON><PERSON>', 'creationdate': '2025-01-30T20:27:03+00:00', 'title': 'Ultimate Data Science & GenAI Bootcamp', 'moddate': '2025-01-30T20:26:59+00:00', 'keywords': 'DAGdmhcqnYw,BAEmsmap8Lg,0', 'author': 'monal singh', 'containsaigeneratedcontent': 'Yes', 'source': 'syllabus.pdf', 'total_pages': 34, 'page': 22, 'page_label': '23'}, page_content='This module introduces you to deep learning and the fundamental concepts behind artificial\\nneural networks (ANNs). You’ll learn about the architecture and workings of a neural network,\\nincluding activation functions, loss functions, and optimization techniques. The module also covers\\nbackpropagation and the vanishing gradient problem. By the end, you’ll be equipped to build and\\ntrain basic neural networks and understand how deep learning models are used in AI applications.\\nIntroduction to Deep Learning and Neural\\nNetworks\\nTopics\\nIntroduction to Deep Learning Why Deep Learning Is Becoming\\nPopular?\\nPerceptron Intuition Understanding the Perceptron Model,\\nBasic Working Principle\\nArtificial Neural Network (ANN)\\nWorking\\nStructure of ANN, Neurons, Layers, and\\nHow Data Passes Through the Network\\nBackpropagation in ANN The Backpropagation Process, Gradient\\nDescent, and Training Networks\\nVanishing Gradient Problem Explanation, Causes, and Solutions\\nExploding Gradient Problem Causes and Mitigation Techniques\\nUltimate Data Science & GenAI Bootcamp       Page  23\\nModule 14'),\n", " Document(metadata={'producer': '<PERSON><PERSON>', 'creator': '<PERSON><PERSON>', 'creationdate': '2025-01-30T20:27:03+00:00', 'title': 'Ultimate Data Science & GenAI Bootcamp', 'moddate': '2025-01-30T20:26:59+00:00', 'keywords': 'DAGdmhcqnYw,BAEmsmap8Lg,0', 'author': 'monal singh', 'containsaigeneratedcontent': 'Yes', 'source': 'syllabus.pdf', 'total_pages': 34, 'page': 23, 'page_label': '24'}, page_content=\"Introduction to Deep Learning and Neural\\nNetworks\\nTopics\\nActivation Functions Different Types of Activation Functions\\n(Sigmoid, ReLU, Tanh, etc.)\\nLoss Functions Common Loss Functions for Regression\\nand Classification\\nOptimizers Types of Optimizers (SGD, Adam,\\nRMSprop, etc.)\\nWeight Initialization Techniques Methods for Initializing Weights (Xavier,\\nHe Initialization)\\nDropout Layer Concept of Dropout and its Role in\\nRegularization\\nBatch Normalization How Batch Normalization Works and\\nWhy It's Important\\nKeras Framework Fundamentals Introduction to Keras, Building Models\\nwith Keras, Basic Operations\\nPyTorch Framework Fundamentals Introduction to PyTorch, Tensor\\nOperations, Building Models with\\nPyTorch\\nUltimate Data Science & GenAI Bootcamp       Page  24\\nModule 14\"),\n", " Document(metadata={'producer': '<PERSON><PERSON>', 'creator': '<PERSON><PERSON>', 'creationdate': '2025-01-30T20:27:03+00:00', 'title': 'Ultimate Data Science & GenAI Bootcamp', 'moddate': '2025-01-30T20:26:59+00:00', 'keywords': 'DAGdmhcqnYw,BAEmsmap8Lg,0', 'author': 'monal singh', 'containsaigeneratedcontent': 'Yes', 'source': 'syllabus.pdf', 'total_pages': 34, 'page': 24, 'page_label': '25'}, page_content='In this module, you’ll dive into Convolutional Neural Networks (CNNs), a cornerstone of deep\\nlearning in computer vision. You’ll learn the architecture of CNNs, including convolution layers,\\npooling layers, and fully connected layers. The module covers practical applications like image\\nclassification, object detection, and segmentation using CNNs. By the end, you’ll have hands-on\\nexperience building and training CNNs for real-world vision tasks.\\nDeep Learning : Convolutional Neural\\nNetworks (CNN) Fundamentals and\\nApplications\\nTopics\\nIntroduction to CNN CNN Fundamentals, What is\\nConvolutional Neural Network, CNN\\nArchitecture Overview\\nExplaining CNN in Detail CNN Explained in Detail, Understanding\\nTensor Space, CNN Explainer\\nCNN-Based Architectures Various CNN Architectures, Deep Dive\\ninto ResNet and its Variants\\nTraining CNN from Scratch Steps to Train CNNs, Hyperparameter\\nTuning, Overfitting, and Underfitting\\nBuilding Web Apps for CNN Deploying CNN Models into Web\\nApplications, Using Flask or Django,\\nServing Models with TensorFlow.js\\nExploding Gradient Problem Causes and Mitigation Techniques\\nUltimate Data Science & GenAI Bootcamp       Page  25\\nModule 15'),\n", " Document(metadata={'producer': '<PERSON><PERSON>', 'creator': '<PERSON><PERSON>', 'creationdate': '2025-01-30T20:27:03+00:00', 'title': 'Ultimate Data Science & GenAI Bootcamp', 'moddate': '2025-01-30T20:26:59+00:00', 'keywords': 'DAGdmhcqnYw,BAEmsmap8Lg,0', 'author': 'monal singh', 'containsaigeneratedcontent': 'Yes', 'source': 'syllabus.pdf', 'total_pages': 34, 'page': 25, 'page_label': '26'}, page_content='Deep Learning : Convolutional Neural\\nNetworks (CNN) Fundamentals and\\nApplications\\nTopics\\nObject Detection Using YOLO Introduction to YOLO (You Only Look\\nOnce), YOLO Architecture, Training and\\nDeployment\\nObject Detection Using Detectron2 Understanding Detectron2 for Object\\nDetection, Using Pre-trained Models and\\nFine-tuning\\nSegmentation Using YOLO Semantic and Instance Segmentation\\nwith YOLO, Implementing YOLO for\\nSegmentation Tasks\\nSegmentation Using Detectron2 Using Detectron2 for Semantic and\\nInstance Segmentation, Implementing\\nPre-trained Models for Image\\nSegmentation\\nUltimate Data Science & GenAI Bootcamp       Page  26\\nModule 15'),\n", " Document(metadata={'producer': '<PERSON><PERSON>', 'creator': '<PERSON><PERSON>', 'creationdate': '2025-01-30T20:27:03+00:00', 'title': 'Ultimate Data Science & GenAI Bootcamp', 'moddate': '2025-01-30T20:26:59+00:00', 'keywords': 'DAGdmhcqnYw,BAEmsmap8Lg,0', 'author': 'monal singh', 'containsaigeneratedcontent': 'Yes', 'source': 'syllabus.pdf', 'total_pages': 34, 'page': 26, 'page_label': '27'}, page_content=\"This module covers Recurrent Neural Networks (RNNs) and Transformer models, focusing on their\\napplications in sequential data processing. You’ll learn how RNNs and LSTMs are used for time\\nseries analysis, speech recognition, and language modeling. The module also explores the\\nTransformer architecture, which powers models like BERT and GPT. By the end, you'll have a\\nstrong grasp of these advanced neural network architectures and their applications in NLP and\\nbeyond.\\nDeep Learning : Recurrent Neural\\nNetworks (RNN) and Transformer\\nModels\\nTopics\\nIntroduction to RNNs Recurrent Neural Networks (RNN)\\nFundamentals, How RNNs Work,\\nApplications of RNN\\nLong Short Term Memory (LSTM) LSTM Cells, How LSTM Solves Vanishing\\nGradient Problem, LSTM for Sequence\\nModeling, Training and Tuning LSTM\\nGated Recurrent Units (GRU) GRU vs LSTM, Understanding GRU\\nArchitecture, Advantages of GRU in\\nSequence Modeling\\nEncoders and Decoders Encoder-Decoder Architecture,\\nApplications in Machine Translation,\\nSequence-to-Sequence Models\\nAttention Mechanism What is Attention, Types of Attention\\nMechanisms, Soft and Hard Attention\\nUltimate Data Science & GenAI Bootcamp       Page  27\\nModule 16\"),\n", " Document(metadata={'producer': '<PERSON><PERSON>', 'creator': '<PERSON><PERSON>', 'creationdate': '2025-01-30T20:27:03+00:00', 'title': 'Ultimate Data Science & GenAI Bootcamp', 'moddate': '2025-01-30T20:26:59+00:00', 'keywords': 'DAGdmhcqnYw,BAEmsmap8Lg,0', 'author': 'monal singh', 'containsaigeneratedcontent': 'Yes', 'source': 'syllabus.pdf', 'total_pages': 34, 'page': 27, 'page_label': '28'}, page_content='Deep Learning : Recurrent Neural\\nNetworks (RNN) and Transformer\\nModels\\nTopics\\nAttention Neural Networks Self-Attention in Neural Networks,\\nApplying Attention to RNNs, Transformer\\nvs RNN\\nBERT Model BERT (Bidirectional Encoder\\nRepresentations from Transformers),\\nPre-training and Fine-tuning BERT,\\nApplications of BERT in NLP\\nGPT-2 Model GPT-2 (Generative Pre-trained\\nTransformer 2), Autoregressive\\nLanguage Modeling, Fine-tuning GPT-2\\nfor Text Generation\\nUltimate Data Science & GenAI Bootcamp       Page  28\\nModule 16'),\n", " Document(metadata={'producer': '<PERSON><PERSON>', 'creator': '<PERSON><PERSON>', 'creationdate': '2025-01-30T20:27:03+00:00', 'title': 'Ultimate Data Science & GenAI Bootcamp', 'moddate': '2025-01-30T20:26:59+00:00', 'keywords': 'DAGdmhcqnYw,BAEmsmap8Lg,0', 'author': 'monal singh', 'containsaigeneratedcontent': 'Yes', 'source': 'syllabus.pdf', 'total_pages': 34, 'page': 28, 'page_label': '29'}, page_content='In this module, you’ll explore the world of Generative AI, understanding how these models\\ngenerate new data based on patterns learned from existing data. You’ll compare generative and\\ndiscriminative models and discover their applications in text, image, and audio generation. The\\nmodule also covers advancements in generative models, including GANs and VAEs. By the end,\\nyou’ll be familiar with key concepts and applications of Generative AI.\\nIntroduction to Generative AI\\nTopics\\nOverview of Generative AI What is Generative AI?, Overview of\\nGenerative vs. Discriminative Models,\\nSignificance and Applications of\\nGenerative AI\\nUnderstanding Generative Models How Generative Models Work, Key\\nTypes of Generative Models (e.g., GANs,\\nVAEs), Advantages of Generative Models\\nGenerative AI vs. Discriminative\\nModels\\nKey Differences, Use Cases,\\nPerformance Comparison\\nRecent Advancements and Research Latest Breakthroughs in Generative AI,\\nState-of-the-Art Models and\\nTechniques, Future Trends in Generative\\nAI\\nKey Applications of Generative\\nModels\\nApplications in Art and Creativity (e.g.,\\nImage Synthesis), Healthcare (e.g., Drug\\nDiscovery), Natural Language\\nProcessing, and More\\nUltimate Data Science & GenAI Bootcamp       Page  29\\nModule 17'),\n", " Document(metadata={'producer': '<PERSON><PERSON>', 'creator': '<PERSON><PERSON>', 'creationdate': '2025-01-30T20:27:03+00:00', 'title': 'Ultimate Data Science & GenAI Bootcamp', 'moddate': '2025-01-30T20:26:59+00:00', 'keywords': 'DAGdmhcqnYw,BAEmsmap8Lg,0', 'author': 'monal singh', 'containsaigeneratedcontent': 'Yes', 'source': 'syllabus.pdf', 'total_pages': 34, 'page': 29, 'page_label': '30'}, page_content='This module introduces you to the concept of vector databases, which are designed to store and\\nretrieve high-dimensional data vectors. You’ll learn how vector databases differ from traditional\\nSQL and NoSQL databases, and explore their use cases, including similarity searches and machine\\nlearning applications. The module also covers popular vector databases like Faiss, Pinecone, and\\nChromaDB. By the end, you’ll be equipped to work with vector databases for handling complex\\ndata queries.\\nIntroduction to Vector Databases\\nTopics\\nOverview of Vector Databases What are Vector Databases?, Key\\nConcepts and Use Cases of Vector\\nDatabases, Difference Between Vector\\nDatabases and Traditional Databases\\nComparison with SQL and NoSQL\\nDatabases\\nSQL vs. NoSQL vs. Vector Databases:\\nKey Differences, Use Cases, and\\nPerformance Considerations\\nCapabilities of Vector Databases Handling High-Dimensional Data, Fast\\nSimilarity Search, Efficient Storage and\\nQuerying, Real-Time Processing\\nData Storage and Architecture of\\nVector Databases\\nStructure of Vector Data, Indexing\\nTechniques, Optimizations for Vector\\nSearch, Performance Considerations\\nTypes of Vector Databases In-Memory Vector Databases: Benefits\\nand Limitations, Local Disk-based Vector\\nDatabases, Cloud-Based Vector\\nDatabases and Their Use Cases\\nUltimate Data Science & GenAI Bootcamp       Page  30\\nModule 18'),\n", " Document(metadata={'producer': '<PERSON><PERSON>', 'creator': '<PERSON><PERSON>', 'creationdate': '2025-01-30T20:27:03+00:00', 'title': 'Ultimate Data Science & GenAI Bootcamp', 'moddate': '2025-01-30T20:26:59+00:00', 'keywords': 'DAGdmhcqnYw,BAEmsmap8Lg,0', 'author': 'monal singh', 'containsaigeneratedcontent': 'Yes', 'source': 'syllabus.pdf', 'total_pages': 34, 'page': 30, 'page_label': '31'}, page_content='Introduction to Vector Databases\\nTopics\\nExploring Popular Vector Databases Chroma DB, Faiss, Quadrant, Pinecone,\\nLanceDB: Overview, Features, and Use\\nCases\\nVector Search with NoSQL Databases Integrating Vector Search with\\nMongoDB and Cassandra, Best\\nPractices for Implementing Vector\\nSearch in NoSQL Databases\\nUltimate Data Science & GenAI Bootcamp       Page  31\\nModule 18'),\n", " Document(metadata={'producer': '<PERSON><PERSON>', 'creator': '<PERSON><PERSON>', 'creationdate': '2025-01-30T20:27:03+00:00', 'title': 'Ultimate Data Science & GenAI Bootcamp', 'moddate': '2025-01-30T20:26:59+00:00', 'keywords': 'DAGdmhcqnYw,BAEmsmap8Lg,0', 'author': 'monal singh', 'containsaigeneratedcontent': 'Yes', 'source': 'syllabus.pdf', 'total_pages': 34, 'page': 31, 'page_label': '32'}, page_content='This module introduces the concept of Retrieval-Augmented Generation (RAG), which combines\\nretrieval-based search with generative models for enhanced language generation tasks. You’ll\\nlearn about the end-to-end RAG pipeline, including how to implement it with tools like LangChain,\\nvector databases, and LLMs. The module also covers hybrid search, reranking, and multimodal\\nretrieval techniques. By the end, you’ll understand how to implement advanced RAG systems for\\nvarious use cases.\\nIntroduction to Retrieval-Augmented\\nGeneration (RAG)\\nTopics\\nOverview of Retrieval-Augmented\\nGeneration (RAG)\\nWhat is RAG?, Key Components of a\\nRAG System, Why RAG is Important for\\nAdvanced AI Systems\\nUnderstanding the End-to-End RAG\\nPipeline\\nOverview of the RAG Workflow, Data\\nRetrieval, Contextualization, and\\nGeneration Phases, Challenges and\\nOpportunities in RAG\\nIntegrating LangChain in RAG Introduction to LangChain Framework,\\nBuilding End-to-End RAG Pipelines with\\nLangChain\\nLeveraging Vector Databases in RAG Using Vector Databases for Efficient\\nRetrieval in RAG, Popular Vector\\nDatabases for RAG (e.g., Pinecone,\\nFAISS, Chroma DB)\\nRole of LLMs in RAG How LLMs (Large Language Models)\\nEnhance Generation in RAG, Fine-\\nTuning LLMs for Retrieval-Augmented\\nTasks\\nUltimate Data Science & GenAI Bootcamp       Page  32\\nModule 19'),\n", " Document(metadata={'producer': '<PERSON><PERSON>', 'creator': '<PERSON><PERSON>', 'creationdate': '2025-01-30T20:27:03+00:00', 'title': 'Ultimate Data Science & GenAI Bootcamp', 'moddate': '2025-01-30T20:26:59+00:00', 'keywords': 'DAGdmhcqnYw,BAEmsmap8Lg,0', 'author': 'monal singh', 'containsaigeneratedcontent': 'Yes', 'source': 'syllabus.pdf', 'total_pages': 34, 'page': 32, 'page_label': '33'}, page_content='Introduction to Retrieval-Augmented\\nGeneration (RAG)\\nTopics\\nRAG with Hybrid Search and\\nReranking\\nCombining Multiple Retrieval Methods,\\nReranking Results for Improved\\nRelevance, Hybrid Search\\nImplementation Techniques\\nRAG with Various Retrieval Methods Exact vs Approximate Retrieval Methods,\\nFiltering and Ranking Retrieved Data,\\nCustomizing Retrieval Approaches for\\nSpecific Applications\\nIntegrating Memory in RAG Systems How Memory Can Improve RAG,\\nPersisting and Recalling Information for\\nConsistent Results, Implementing Long-\\nTerm Memory in RAG\\nMultimodal Retrieval-Augmented\\nGeneration\\nCombining Text, Images, and Other\\nModalities in RAG, Techniques for\\nMultimodal Retrieval and Generation,\\nPractical Applications of Multimodal\\nRAG Systems\\nUltimate Data Science & GenAI Bootcamp       Page  33\\nModule 19'),\n", " Document(metadata={'producer': '<PERSON><PERSON>', 'creator': '<PERSON><PERSON>', 'creationdate': '2025-01-30T20:27:03+00:00', 'title': 'Ultimate Data Science & GenAI Bootcamp', 'moddate': '2025-01-30T20:26:59+00:00', 'keywords': 'DAGdmhcqnYw,BAEmsmap8Lg,0', 'author': 'monal singh', 'containsaigeneratedcontent': 'Yes', 'source': 'syllabus.pdf', 'total_pages': 34, 'page': 33, 'page_label': '34'}, page_content='In this course, you’ll gain hands-on experience in implementing end-to-end AI projects. You’ll learn\\nhow to manage the entire project lifecycle, from data collection and preprocessing to model\\ndevelopment, evaluation, and deployment. The module includes working on real-world AI projects,\\nwith a focus on best practices for integration, testing, and scalability. By the end, you’ll be\\nprepared to take on AI projects from start to finish, applying machine learning and deep learning\\ntechniques to solve real-world problems.\\nEnd-to-End AI Project Implementation\\nTopics\\nPython Project: Building End-to-End\\nApplications\\nOverview of Python Projects, Project\\nDesign and Architecture, Key\\nConsiderations in Python Projects\\n(Performance, Scalability, etc.), Best\\nPractices for Code Quality\\nEnd-to-End Machine Learning\\nProjects\\nUnderstanding End-to-End ML Projects,\\nKey Components of an End-to-End ML\\nProject, Project Example: Real-World ML\\nApplication\\nDeep Learning Projects Deep Learning Fundamentals in Projects,\\nEnd-to-End Deep Learning Projects \\nGenerative AI End-to-End Projects Introduction to Generative AI Projects,\\nSteps in Building Generative AI Projects\\nUltimate Data Science & GenAI Bootcamp       Page  34\\nPROJECT')]"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["### Read a PDf file\n", "from langchain_community.document_loaders import PyPDFLoader\n", "loader=PyPDFLoader('syllabus.pdf')\n", "docs=loader.load()\n", "docs"]}, {"cell_type": "code", "execution_count": 19, "id": "120674d0", "metadata": {}, "outputs": [{"data": {"text/plain": ["[Document(metadata={'source': 'https://python.langchain.com/docs/integrations/document_loaders/', 'title': 'Document loaders | 🦜️🔗 LangChain', 'description': 'DocumentLoaders load data into the standard LangChain Document format.', 'language': 'en'}, page_content='\\n\\n\\n\\n\\nDocument loaders | 🦜️🔗 LangChain\\n\\n\\n\\n\\n\\n\\nSkip to main contentWe are growing and hiring for multiple roles for LangChain, LangGraph and LangSmith.  Join our team!IntegrationsAPI ReferenceMoreContributingPeopleError referenceLangSmithLangGraphLangChain HubLangChain JS/TSv0.3v0.3v0.2v0.1💬SearchProvidersAnthropicAWSGoogleHugging FaceMicrosoftOpenAIMoreProvidersAbsoAcreomActiveloop Deep LakeADS4GPTsAerospikeAgentQLAI21 LabsAimAINetworkAirbyteAirtableAlchemyAleph AlphaAlibaba CloudAnalyticDBAnnoyAnthropicAnyscaleApache Software FoundationApache DorisApifyAppleArangoDBArceeArcGISArgillaArizeArthurArxivAscendAskNewsAssemblyAIAstra DBAtlasAwaDBAWSAZLyricsAzure AIBAAIBagelBagelDBBaichuanBaiduBananaBasetenBeamBeautiful SoupBibTeXBiliBiliBittensorBlackboardbookend.aiBoxBrave SearchBreebs (Open Knowledge)Bright DataBrowserbaseBrowserlessByteDanceCassandraCerebrasCerebriumAIChaindeskChromaClarifaiClearMLClickHouseClickUpCloudflareClovaCnosDBCogneeCogniSwitchCohereCollege ConfidentialCometConfident AIConfluenceConneryContextContextual AICouchbaseCozeCrateDBC TransformersCTranslate2CubeDappierDashVectorDatabricksDatadog TracingDatadog LogsDataForSEODataheraldDedocDeepInfraDeeplakeDeepSeekDeepSparseDellDiffbotDingoDBDiscordDiscord (community loader)DocArrayDoclingDoctranDocugamiDocusaurusDriaDropboxDuckDBDuckDuckGo SearchE2BEden AIElasticsearchElevenLabsEmbedchainEpsillaEtherscanEverly AIEverNoteExaFacebook - MetaFalkorDBFaunaFeatherless AIFiddlerFigmaFireCrawlFireworks AIFlyteFMP Data (Financial Data Prep)Forefront AIFriendli AISmabblerGelGeopandasGitGitBookGitHubGitLabGOATGoldenGoodfireGoogleSerper - Google Search APIGooseAIGPT4AllGradientGraph RAGGraphsignalGrobidGroqGutenbergHacker NewsHazy ResearchHeliconeHologresHTML to textHuaweiHugging FaceHyperbrowserIBMIEIT SystemsiFixitiFlytekIMSDbInfinispan VSInfinityInfinoIntelIuguJaguarJavelin AI GatewayJenkinsJina AIJohnsnowlabsJoplinKDB.AIKineticaKoboldAIKonkoKoNLPYKùzuLabel StudiolakeFSLanceDBLangChain Decorators ✨LangFair: Use-Case Level LLM Bias and Fairness AssessmentsLangfuse 🪢LanternLindormLinkupLiteLLMLlamaIndexLlama.cppLlamaEdgellamafileLLMonitorLocalAILog10MariaDBMariTalkMarqoMediaWikiDumpMeilisearchMemcachedMemgraphMetalMicrosoftMilvusMindsDBMinimaxMistralAIMLflow AI Gateway for LLMsMLflowMLXModalModelScopeModern TreasuryMomentoMongoDBMongoDB AtlasMotherduckMotörheadMyScaleNAVERNeo4jNetmindNimbleNLPCloudNomicNotion DBNucliaNVIDIAObsidianOceanBaseOracle Cloud Infrastructure (OCI)OctoAIOllamaOntotext GraphDBOpenAIOpenGradientOpenLLMOpenSearchOpenWeatherMapOracleAI Vector SearchOutlineOutlinesOxylabsPandasPaymanAIPebbloPermitPerplexityPetalsPostgres EmbeddingPGVectorPineconePipelineAIPipeshiftPortkeyPredibasePrediction GuardPremAISWI-PrologPromptLayerPsychicPubMedPullMd LoaderPygmalionAIPyMuPDF4LLMQdrantRAGatouillerank_bm25Ray ServeRebuffRedditRedisRemembrallReplicateRoamSema4 (fka Robocorp)RocksetRunhouseRunpodRWKV-4SalesforceSambaNovaSAPScrapeGraph AISearchApiSearxNG Search APISemaDBSerpAPIShale ProtocolSingleStore Integrationscikit-learnSlackSnowflakespaCySparkSparkLLMSpreedlySQLiteStack ExchangeStarRocksStochasticAIStreamlitStripeSupabase (Postgres)NebulaTableauTaigaTairTavilyTelegramTencentTensorFlow DatasetsTiDBTigerGraphTigrisTiloresTogether AI2MarkdownTranswarpTrelloTrubricsTruLensTwitterTypesenseUnstructuredUpstageupstashUpTrainUSearchValtheraValyu Deep SearchVDMSVearchVectaraVectorizeVespavliteVoyageAIWeights & BiasesWeights & Biases tracingWeights & Biases trackingWeatherWeaviateWhatsAppWhyLabsWikipediaWolfram AlphaWriter, Inc.xAIXataXorbits Inference (Xinference)YahooYandexYDBYeager.aiYellowbrick01.AIYouYouTubeZepZhipu AIZillizZoteroComponentsChat modelsChat modelsAbsoAI21 LabsAlibaba Cloud PAI EASAnthropic[Deprecated] Experimental Anthropic Tools WrapperAnyscaleAzureAIChatCompletionsModelAzure OpenAIAzure ML EndpointBaichuan ChatBaidu QianfanAWS BedrockCerebrasCloudflareWorkersAICohereContextualAICoze ChatDappier AIDatabricksDeepInfraDeepSeekEden AIErnie Bot ChatEverlyAIFeatherless AIFireworksChatFriendliGoodfireGoogle GeminiGoogle Cloud Vertex AIGPTRouterGroqChatHuggingFaceIBM watsonx.aiJinaChatKineticaKonkoLiteLLMLlama 2 ChatLlama APILlamaEdgeLlama.cppmaritalkMiniMaxMistralAIMLXModelScopeMoonshotNaverNetmindNVIDIA AI EndpointsChatOCIModelDeploymentOCIGenAIChatOctoAIOllamaOpenAIOutlinesPerplexityPipeshiftChatPredictionGuardPremAIPromptLayer ChatOpenAIQwen QwQRekaRunPod Chat ModelSambaNovaCloudSambaStudioChatSeekrFlowSnowflake CortexsolarSparkLLM ChatNebula (Symbl.ai)Tencent HunyuanTogetherTongyi QwenUpstagevectaravLLM ChatVolc Engine MaasChat WriterxAIXinferenceYandexGPTChatYIYuan2.0ZHIPU AIRetrieversRetrieversActiveloop Deep MemoryAmazon KendraArceeArxivAskNewsAzure AI SearchBedrock (Knowledge Bases)BM25BoxBREEBS (Open Knowledge)ChaindeskChatGPT pluginCogneeCohere rerankerCohere RAGContextual AI RerankerDappierDocArrayDriaElasticSearch BM25ElasticsearchEmbedchainFlashRank rerankerFleet AI ContextGalaxiaGoogle DriveGoogle Vertex AI SearchGraph RAGIBM watsonx.aiJaguarDB Vector DatabaseKay.aiKinetica Vectorstore based RetrieverkNNLinkupSearchRetrieverLLMLingua Document CompressorLOTR (Merger Retriever)MetalNanoPQ (Product Quantization)needleNimbleOutlinePermitPinecone Hybrid SearchPinecone RerankPubMedQdrant Sparse VectorRAGatouilleRePhraseQueryRememberizerSEC filingSelf-querying retrieversSVMTavilySearchAPITF-IDF**NeuralDB**ValyuContextVectorizeVespaWikipediaYou.comZep CloudZep Open SourceZilliz Cloud PipelineZoteroTools/ToolkitsToolsADS4GPTsAgentQLAINetwork ToolkitAlpha VantageAmadeus ToolkitApify ActorArXivAskNewsAWS LambdaAzure AI Services ToolkitAzure Cognitive Services ToolkitAzure Container Apps dynamic sessionsShell (bash)Bearly Code InterpreterBing SearchBrave SearchBrightDataWebScraperAPIBrightDataSERPBrightDataUnlockerCassandra Database ToolkitCDPChatGPT PluginsClickUp ToolkitCogniswitch ToolkitCompass DeFi ToolkitConnery Toolkit and ToolsDall-E Image GeneratorDappierDatabricks Unity Catalog (UC)DataForSEODataheraldDuckDuckGo SearchDiscordE2B Data AnalysisEden AIElevenLabs Text2SpeechExa SearchFile SystemFinancialDatasets ToolkitFMP DataGithub ToolkitGitlab ToolkitGmail ToolkitGOATGolden QueryGoogle BooksGoogle Calendar ToolkitGoogle Cloud Text-to-SpeechGoogle DriveGoogle FinanceGoogle ImagenGoogle JobsGoogle LensGoogle PlacesGoogle ScholarGoogle SearchGoogle SerperGoogle TrendsGradioGraphQLHuggingFace Hub ToolsHuman as a toolHyperbrowser Browser Agent ToolsHyperbrowser Web Scraping ToolsIBM watsonx.aiIFTTT WebHooksInfobipIonic Shopping ToolJenkinsJina SearchJira ToolkitJSON ToolkitLemon AgentLinkupSearchToolMemgraphMemorizeMojeek SearchMultiOn ToolkitNASA ToolkitNaver SearchNuclia UnderstandingNVIDIA Riva: ASR and TTSOffice365 ToolkitOpenAPI ToolkitNatural Language API ToolkitsOpenGradientOpenWeatherMapOracle AI Vector Search: Generate SummaryOxylabsPandas DataframePassio NutritionAIPaymanAIPermitPlayWright Browser ToolkitPolygon IO Toolkit and ToolsPowerBI ToolkitPrologPubMedPython REPLReddit SearchRequests ToolkitRiza Code InterpreterRobocorp ToolkitSalesforceSceneXplainScrapeGraphSearchApiSearxNG SearchSemantic Scholar API ToolSerpAPISlack ToolkitSpark SQL ToolkitSQLDatabase ToolkitStackExchangeSteam ToolkitStripeTableauTaigaTavily ExtractTavily SearchTiloresTwilioUpstageValtheraValyuContextVectaraWikidataWikipediaWolfram AlphaWriter ToolsYahoo Finance NewsYou.com SearchYouTubeZapier Natural Language ActionsZenGuard AIDocument loadersDocument loadersacreomAgentQLLoaderAirbyteLoaderAirbyte CDK (Deprecated)Airbyte Gong (Deprecated)Airbyte Hubspot (Deprecated)Airbyte JSON (Deprecated)Airbyte Salesforce (Deprecated)Airbyte Shopify (Deprecated)Airbyte Stripe (Deprecated)Airbyte Typeform (Deprecated)Airbyte Zendesk Support (Deprecated)AirtableAlibaba Cloud MaxComputeAmazon TextractApify DatasetArcGISArxivLoaderAssemblyAI Audio TranscriptsAstraDBAsync ChromiumAsyncHtmlAthenaAWS S3 DirectoryAWS S3 FileAZLyricsAzure AI DataAzure Blob Storage ContainerAzure Blob Storage FileAzure AI Document IntelligenceBibTeXBiliBiliBlackboardBlockchainBoxBrave SearchBrowserbaseBrowserlessBSHTMLLoaderCassandraChatGPT DataCollege ConfidentialConcurrent LoaderConfluenceCoNLL-UCopy PasteCouchbaseCSVCube Semantic LayerDatadog LogsDedocDiffbotDiscordDoclingDocugamiDocusaurusDropboxDuckDBEmailEPubEtherscanEverNoteexample_dataFacebook ChatFaunaFigmaFireCrawlGeopandasGitGitBookGitHubGlue CatalogGoogle AlloyDB for PostgreSQLGoogle BigQueryGoogle BigtableGoogle Cloud SQL for SQL serverGoogle Cloud SQL for MySQLGoogle Cloud SQL for PostgreSQLGoogle Cloud Storage DirectoryGoogle Cloud Storage FileGoogle Firestore in Datastore ModeGoogle DriveGoogle El Carro for Oracle WorkloadsGoogle Firestore (Native Mode)Google Memorystore for RedisGoogle SpannerGoogle Speech-to-Text Audio TranscriptsGrobidGutenbergHacker NewsHuawei OBS DirectoryHuawei OBS FileHuggingFace datasetHyperbrowserLoaderiFixitImagesImage captionsIMSDbIuguJoplinJSONLoaderJupyter NotebookKineticalakeFSLangSmithLarkSuite (FeiShu)LLM SherpaMastodonMathPixPDFLoaderMediaWiki DumpMerge Documents LoadermhtmlMicrosoft ExcelMicrosoft OneDriveMicrosoft OneNoteMicrosoft PowerPointMicrosoft SharePointMicrosoft WordNear BlockchainModern TreasuryMongoDBNeedle Document LoaderNews URLNotion DB 2/2NucliaObsidianOpen Document Format (ODT)Open City DataOracle Autonomous DatabaseOracle AI Vector Search: Document ProcessingOrg-modeOutline Document LoaderPandas DataFrameparsersPDFMinerLoaderPDFPlumberPebblo Safe DocumentLoaderPolars DataFrameDell PowerScale Document LoaderPsychicPubMedPullMdLoaderPyMuPDFLoaderPyMuPDF4LLMPyPDFDirectoryLoaderPyPDFium2LoaderPyPDFLoaderPySparkQuipReadTheDocs DocumentationRecursive URLRedditRoamRocksetrspaceRSS FeedsRSTscrapflyScrapingAntSingleStoreSitemapSlackSnowflakeSource CodeSpiderSpreedlyStripeSubtitleSurrealDBTelegramTencent COS DirectoryTencent COS FileTensorFlow DatasetsTiDB2MarkdownTOMLTrelloTSVTwitterUnstructuredUnstructuredMarkdownLoaderUnstructuredPDFLoaderUpstageURLVsdxWeatherWebBaseLoaderWhatsApp ChatWikipediaUnstructuredXMLLoaderXorbits Pandas DataFrameYouTube audioYouTube transcriptsYoutubeLoaderDLYuqueZeroxPDFLoaderVector storesVector storesActiveloop Deep LakeAerospikeAlibaba Cloud OpenSearchAnalyticDBAnnoyApache DorisApertureDBAstra DB Vector StoreAtlasAwaDBAzure Cosmos DB Mongo vCoreAzure Cosmos DB No SQLAzure AI SearchBagelBagelDBBaidu Cloud ElasticSearch VectorSearchBaidu VectorDBApache CassandraChromaClarifaiClickHouseCloudflareVectorizeCouchbaseDashVectorDatabricksDingoDBDocArray HnswSearchDocArray InMemorySearchAmazon Document DBDuckDBChina Mobile ECloud ElasticSearch VectorSearchElasticsearchEpsillaFaissFaiss (Async)FalkorDBVectorStoreGelGoogle AlloyDB for PostgreSQLGoogle BigQuery Vector SearchGoogle Cloud SQL for MySQLGoogle Cloud SQL for PostgreSQLFirestoreGoogle Memorystore for RedisGoogle SpannerGoogle Vertex AI Feature StoreGoogle Vertex AI Vector SearchHippoHologresInfinispanJaguar Vector DatabaseKDB.AIKineticaLanceDBLanternLindormLLMRailsManticoreSearch VectorStoreMariaDBMarqoMeilisearchAmazon MemoryDBMilvusMomento Vector Index (MVI)MongoDB AtlasMyScaleNeo4j Vector IndexNucliaDBOceanbaseopenGaussOpenSearchOracle AI Vector Search: Vector StorePathwayPostgres EmbeddingPGVecto.rsPGVectorPineconePinecone (sparse)QdrantRedisRelytRocksetSAP HANA Cloud Vector EngineScaNNSemaDBSingleStorescikit-learnSQLiteVecSQLite-VSSSQLServerStarRocksSupabase (Postgres)SurrealDBTablestoreTairTencent Cloud VectorDBThirdAI NeuralDBTiDB VectorTigrisTileDBTimescale Vector (Postgres)TypesenseUpstash VectorUSearchValdVDMSVearchVectaraVespaviking DBvliteWeaviateXataYDBYellowbrickZepZep CloudZillizEmbedding modelsEmbedding modelsAI21Aleph AlphaAnyscaleascendAwaDBAzureOpenAIBaichuan Text EmbeddingsBaidu QianfanBedrockBGE on Hugging FaceBookend AIClarifaiCloudflare Workers AIClova EmbeddingsCohereDashScopeDatabricksDeepInfraEDEN AIElasticsearchEmbaasERNIEFake EmbeddingsFastEmbed by QdrantFireworksGoogle GeminiGoogle Vertex AIGPT4AllGradientHugging FaceIBM watsonx.aiInfinityInstruct Embeddings on Hugging FaceIPEX-LLM: Local BGE Embeddings on Intel CPUIPEX-LLM: Local BGE Embeddings on Intel GPUIntel® Extension for Transformers Quantized Text EmbeddingsJinaJohn Snow LabsLASER Language-Agnostic SEntence Representations Embeddings by Meta AILindormLlama.cppllamafileLLMRailsLocalAIMiniMaxMistralAImodel2vecModelScopeMosaicMLNaverNetmindNLP CloudNomicNVIDIA NIMsOracle Cloud Infrastructure Generative AIOllamaOpenClipOpenAIOpenVINOEmbedding Documents using Optimized and Quantized EmbeddersOracle AI Vector Search: Generate EmbeddingsOVHcloudPinecone EmbeddingsPredictionGuardEmbeddingsPremAISageMakerSambaNovaCloudSambaStudioSelf HostedSentence Transformers on Hugging FaceSolarSpaCySparkLLM Text EmbeddingsTensorFlow HubText Embeddings InferenceTextEmbed - Embedding Inference ServerTitan TakeoffTogether AIUpstageVolc EngineVoyage AIXorbits inference (Xinference)YandexGPTZhipuAIOtherComponentsDocument loadersOn this pageDocument loaders\\n\\nDocumentLoaders load data into the standard LangChain Document format.\\nEach DocumentLoader has its own specific parameters, but they can all be invoked in the same way with the .load method.\\nAn example use case is as follows:\\nfrom langchain_community.document_loaders.csv_loader import CSVLoaderloader = CSVLoader(    ...  # <-- Integration specific parameters here)data = loader.load()API Reference:CSVLoader\\nWebpages\\u200b\\nThe below document loaders allow you to load webpages.\\nSee this guide for a starting point: How to: load web pages.\\nDocument LoaderDescriptionPackage/APIWebUses urllib and BeautifulSoup to load and parse HTML web pagesPackageUnstructuredUses Unstructured to load and parse web pagesPackageRecursiveURLRecursively scrapes all child links from a root URLPackageSitemapScrapes all pages on a given sitemapPackageFirecrawlAPI service that can be deployed locally, hosted version has free credits.APIDoclingUses Docling to load and parse web pagesPackageHyperbrowserPlatform for running and scaling headless browsers, can be used to scrape/crawl any siteAPIAgentQLWeb interaction and structured data extraction from any web page using an AgentQL query or a Natural Language promptAPI\\nPDFs\\u200b\\nThe below document loaders allow you to load PDF documents.\\nSee this guide for a starting point: How to: load PDF files.\\nDocument LoaderDescriptionPackage/APIPyPDFUses `pypdf` to load and parse PDFsPackageUnstructuredUses Unstructured\\'s open source library to load PDFsPackageAmazon TextractUses AWS API to load PDFsAPIMathPixUses MathPix to load PDFsPackagePDFPlumberLoad PDF files using PDFPlumberPackagePyPDFDirectryLoad a directory with PDF filesPackagePyPDFium2Load PDF files using PyPDFium2PackagePyMuPDFLoad PDF files using PyMuPDFPackagePyMuPDF4LLMLoad PDF content to Markdown using PyMuPDF4LLMPackagePDFMinerLoad PDF files using PDFMinerPackageUpstage Document Parse LoaderLoad PDF files using UpstageDocumentParseLoaderPackageDoclingLoad PDF files using DoclingPackage\\nCloud Providers\\u200b\\nThe below document loaders allow you to load documents from your favorite cloud providers.\\nDocument LoaderDescriptionPartner PackageAPI referenceAWS S3 DirectoryLoad documents from an AWS S3 directory❌S3DirectoryLoaderAWS S3 FileLoad documents from an AWS S3 file❌S3FileLoaderAzure AI DataLoad documents from Azure AI services❌AzureAIDataLoaderAzure Blob Storage ContainerLoad documents from an Azure Blob Storage container❌AzureBlobStorageContainerLoaderAzure Blob Storage FileLoad documents from an Azure Blob Storage file❌AzureBlobStorageFileLoaderDropboxLoad documents from Dropbox❌DropboxLoaderGoogle Cloud Storage DirectoryLoad documents from GCS bucket✅GCSDirectoryLoaderGoogle Cloud Storage FileLoad documents from GCS file object✅GCSFileLoaderGoogle DriveLoad documents from Google Drive (Google Docs only)✅GoogleDriveLoaderHuawei OBS DirectoryLoad documents from Huawei Object Storage Service Directory❌OBSDirectoryLoaderHuawei OBS FileLoad documents from Huawei Object Storage Service File❌OBSFileLoaderMicrosoft OneDriveLoad documents from Microsoft OneDrive❌OneDriveLoaderMicrosoft SharePointLoad documents from Microsoft SharePoint❌SharePointLoaderTencent COS DirectoryLoad documents from Tencent Cloud Object Storage Directory❌TencentCOSDirectoryLoaderTencent COS FileLoad documents from Tencent Cloud Object Storage File❌TencentCOSFileLoader\\nSocial Platforms\\u200b\\nThe below document loaders allow you to load documents from different social media platforms.\\nDocument LoaderAPI referenceTwitterTwitterTweetLoaderRedditRedditPostsLoader\\nMessaging Services\\u200b\\nThe below document loaders allow you to load data from different messaging platforms.\\nDocument LoaderAPI referenceTelegramTelegramChatFileLoaderWhatsAppWhatsAppChatLoaderDiscordDiscordChatLoaderFacebook ChatFacebookChatLoaderMastodonMastodonTootsLoader\\nProductivity tools\\u200b\\nThe below document loaders allow you to load data from commonly used productivity tools.\\nDocument LoaderAPI referenceFigmaFigmaFileLoaderNotionNotionDirectoryLoaderSlackSlackDirectoryLoaderQuipQuipLoaderTrelloTrelloLoaderRoamRoamLoaderGitHubGithubFileLoader\\nCommon File Types\\u200b\\nThe below document loaders allow you to load data from common data formats.\\nDocument LoaderData TypeCSVLoaderCSV filesDirectoryLoaderAll files in a given directoryUnstructuredMany file types (see https://docs.unstructured.io/platform/supported-file-types)JSONLoaderJSON filesBSHTMLLoaderHTML filesDoclingLoaderVarious file types (see https://ds4sd.github.io/docling/)\\nAll document loaders\\u200b\\nNameDescriptionacreomacreom is a dev-first knowledge base with tasks running on local mark...AgentQLLoaderAgentQL\\'s document loader provides structured data extraction from an...AirbyteLoaderAirbyte is a data integration platform for ELT pipelines from APIs, d...Airtable* Get your API key here.Alibaba Cloud MaxComputeAlibaba Cloud MaxCompute (previously known as ODPS) is a general purp...Amazon TextractAmazon Textract is a machine learning (ML) service that automatically...Apify DatasetApify Dataset is a scalable append-only storage with sequential acces...ArcGISThis notebook demonstrates the use of the langchaincommunity.document...ArxivLoaderarXiv is an open-access archive for 2 million scholarly articles in t...AssemblyAI Audio TranscriptsThe AssemblyAIAudioTranscriptLoader allows to transcribe audio files ...AstraDBDataStax Astra DB is a serverlessAsync ChromiumChromium is one of the browsers supported by Playwright, a library us...AsyncHtmlAsyncHtmlLoader loads raw HTML from a list of URLs concurrently.AthenaAmazon Athena is a serverless, interactive analytics service builtAWS S3 DirectoryAmazon Simple Storage Service (Amazon S3) is an object storage serviceAWS S3 FileAmazon Simple Storage Service (Amazon S3) is an object storage servic...AZLyricsAZLyrics is a large, legal, every day growing collection of lyrics.Azure AI DataAzure AI Studio provides the capability to upload data assets to clou...Azure Blob Storage ContainerAzure Blob Storage is Microsoft\\'s object storage solution for the clo...Azure Blob Storage FileAzure Files offers fully managed file shares in the cloud that are ac...Azure AI Document IntelligenceAzure AI Document Intelligence (formerly known as Azure Form Recogniz...BibTeXBibTeX is a file format and reference management system commonly used...BiliBiliBilibili is one of the most beloved long-form video sites in China.BlackboardBlackboard Learn (previously the Blackboard Learning Management Syste...BlockchainThe intention of this notebook is to provide a means of testing funct...BoxThe langchain-box package provides two methods to index your files fr...Brave SearchBrave Search is a search engine developed by Brave Software.BrowserbaseBrowserbase is a developer platform to reliably run, manage, and moni...BrowserlessBrowserless is a service that allows you to run headless Chrome insta...BSHTMLLoaderThis notebook provides a quick overview for getting started with Beau...CassandraCassandra is a NoSQL, row-oriented, highly scalable and highly availa...ChatGPT DataChatGPT is an artificial intelligence (AI) chatbot developed by OpenA...College ConfidentialCollege Confidential gives information on 3,800+ colleges and univers...Concurrent LoaderWorks just like the GenericLoader but concurrently for those who choo...ConfluenceConfluence is a wiki collaboration platform designed to save and orga...CoNLL-UCoNLL-U is revised version of the CoNLL-X format. Annotations are enc...Copy PasteThis notebook covers how to load a document object from something you...CouchbaseCouchbase is an award-winning distributed NoSQL cloud database that d...CSVA comma-separated values (CSV) file is a delimited text file that use...Cube Semantic LayerThis notebook demonstrates the process of retrieving Cube\\'s data mode...Datadog LogsDatadog is a monitoring and analytics platform for cloud-scale applic...DedocThis sample demonstrates the use of Dedoc in combination with LangCha...DiffbotDiffbot is a suite of ML-based products that make it easy to structur...DiscordDiscord is a VoIP and instant messaging social platform. Users have t...DoclingDocling parses PDF, DOCX, PPTX, HTML, and other formats into a rich u...DocugamiThis notebook covers how to load documents from Docugami. It provides...DocusaurusDocusaurus is a static-site generator which provides out-of-the-box d...DropboxDropbox is a file hosting service that brings everything-traditional ...DuckDBDuckDB is an in-process SQL OLAP database management system.EmailThis notebook shows how to load email (.eml) or Microsoft Outlook (.m...EPubEPUB is an e-book file format that uses the \".epub\" file extension. T...EtherscanEtherscan  is the leading blockchain explorer, search, API and analyt...EverNoteEverNote is intended for archiving and creating notes in which photos...example_dataFacebook ChatMessenger) is an American proprietary instant messaging app and platf...FaunaFauna is a Document Database.FigmaFigma is a collaborative web application for interface design.FireCrawlFireCrawl crawls and convert any website into LLM-ready data. It craw...GeopandasGeopandas is an open-source project to make working with geospatial d...GitGit is a distributed version control system that tracks changes in an...GitBookGitBook is a modern documentation platform where teams can document e...GitHubThis notebooks shows how you can load issues and pull requests (PRs) ...Glue CatalogThe AWS Glue Data Catalog is a centralized metadata repository that a...Google AlloyDB for PostgreSQLAlloyDB is a fully managed relational database service that offers hi...Google BigQueryGoogle BigQuery is a serverless and cost-effective enterprise data wa...Google BigtableBigtable is a key-value and wide-column store, ideal for fast access ...Google Cloud SQL for SQL serverCloud SQL is a fully managed relational database service that offers ...Google Cloud SQL for MySQLCloud SQL is a fully managed relational database service that offers ...Google Cloud SQL for PostgreSQLCloud SQL for PostgreSQL is a fully-managed database service that hel...Google Cloud Storage DirectoryGoogle Cloud Storage is a managed service for storing unstructured da...Google Cloud Storage FileGoogle Cloud Storage is a managed service for storing unstructured da...Google Firestore in Datastore ModeFirestore in Datastore Mode is a NoSQL document database built for au...Google DriveGoogle Drive is a file storage and synchronization service developed ...Google El Carro for Oracle WorkloadsGoogle El Carro Oracle OperatorGoogle Firestore (Native Mode)Firestore is a serverless document-oriented database that scales to m...Google Memorystore for RedisGoogle Memorystore for Redis is a fully-managed service that is power...Google SpannerSpanner is a highly scalable database that combines unlimited scalabi...Google Speech-to-Text Audio TranscriptsThe SpeechToTextLoader allows to transcribe audio files with the Goog...GrobidGROBID is a machine learning library for extracting, parsing, and re-...GutenbergProject Gutenberg is an online library of free eBooks.Hacker NewsHacker News (sometimes abbreviated as HN) is a social news website fo...Huawei OBS DirectoryThe following code demonstrates how to load objects from the Huawei O...Huawei OBS FileThe following code demonstrates how to load an object from the Huawei...HuggingFace datasetThe Hugging Face Hub is home to over 5,000 datasets in more than 100 ...HyperbrowserLoaderHyperbrowser is a platform for running and scaling headless browsers....iFixitiFixit is the largest, open repair community on the web. The site con...ImagesThis covers how to load images into a document format that we can use...Image captionsBy default, the loader utilizes the pre-trained Salesforce BLIP image...IMSDbIMSDb is the Internet Movie Script Database.IuguIugu is a Brazilian services and software as a service (SaaS) company...JoplinJoplin is an open-source note-taking app. Capture your thoughts and s...JSONLoaderThis notebook provides a quick overview for getting started with JSON...Jupyter NotebookJupyter Notebook (formerly IPython Notebook) is a web-based interacti...KineticaThis notebooks goes over how to load documents from KineticalakeFSlakeFS provides scalable version control over the data lake, and uses...LangSmithThis notebook provides a quick overview for getting started with the ...LarkSuite (FeiShu)LarkSuite is an enterprise collaboration platform developed by ByteDa...LLM SherpaThis notebook covers how to use LLM Sherpa to load files of many type...MastodonMastodon is a federated social media and social networking service.MathPixPDFLoaderInspired by Daniel Gross\\'s snippet here//gist.github.com/danielgross/...MediaWiki DumpMediaWiki XML Dumps contain the content of a wiki (wiki pages with al...Merge Documents LoaderMerge the documents returned from a set of specified data loaders.mhtmlMHTML is a is used both for emails but also for archived webpages. MH...Microsoft ExcelThe UnstructuredExcelLoader is used to load Microsoft Excel files. Th...Microsoft OneDriveMicrosoft OneDrive (formerly SkyDrive) is a file hosting service oper...Microsoft OneNoteThis notebook covers how to load documents from OneNote.Microsoft PowerPointMicrosoft PowerPoint is a presentation program by Microsoft.Microsoft SharePointMicrosoft SharePoint is a website-based collaboration system that use...Microsoft WordMicrosoft Word is a word processor developed by Microsoft.Near BlockchainThe intention of this notebook is to provide a means of testing funct...Modern TreasuryModern Treasury simplifies complex payment operations. It is a unifie...MongoDBMongoDB is a NoSQL , document-oriented database that supports JSON-li...Needle Document LoaderNeedle makes it easy to create your RAG pipelines with minimal effort.News URLThis covers how to load HTML news articles from a list of URLs into a...Notion DB 2/2Notion is a collaboration platform with modified Markdown support tha...NucliaNuclia automatically indexes your unstructured data from any internal...ObsidianObsidian is a powerful and extensible knowledge baseOpen Document Format (ODT)The Open Document Format for Office Applications (ODF), also known as...Open City DataSocrata provides an API for city open data.Oracle Autonomous DatabaseOracle autonomous database is a cloud database that uses machine lear...Oracle AI Vector Search: Document ProcessingOracle AI Vector Search is designed for Artificial Intelligence (AI) ...Org-modeA Org Mode document is a document editing, formatting, and organizing...Outline Document LoaderOutline is an open-source collaborative knowledge base platform desig...Pandas DataFrameThis notebook goes over how to load data from a pandas DataFrame.parsersPDFMinerLoaderThis notebook provides a quick overview for getting started with PDFM...PDFPlumberLike PyMuPDF, the output Documents contain detailed metadata about th...Pebblo Safe DocumentLoaderPebblo enables developers to safely load data and promote their Gen A...Polars DataFrameThis notebook goes over how to load data from a polars DataFrame.Dell PowerScale Document LoaderDell PowerScale is an enterprise scale out storage system that hosts ...PsychicThis notebook covers how to load documents from Psychic. See here for...PubMedPubMed® by The National Center for Biotechnology Information, Nationa...PullMdLoaderLoader for converting URLs into Markdown using the pull.md service.PyMuPDFLoaderThis notebook provides a quick overview for getting started with PyMu...PyMuPDF4LLMThis notebook provides a quick overview for getting started with PyMu...PyPDFDirectoryLoaderThis loader loads all PDF files from a specific directory.PyPDFium2LoaderThis notebook provides a quick overview for getting started with PyPD...PyPDFLoaderThis notebook provides a quick overview for getting started with PyPD...PySparkThis notebook goes over how to load data from a PySpark DataFrame.QuipQuip is a collaborative productivity software suite for mobile and We...ReadTheDocs DocumentationRead the Docs is an open-sourced free software documentation hosting ...Recursive URLThe RecursiveUrlLoader lets you recursively scrape all child links fr...RedditReddit is an American social news aggregation, content rating, and di...RoamROAM is a note-taking tool for networked thought, designed to create ...RocksetRockset is a real-time analytics database which enables queries on ma...rspaceThis notebook shows how to use the RSpace document loader to import r...RSS FeedsThis covers how to load HTML news articles from a list of RSS feed UR...RSTA reStructured Text (RST) file is a file format for textual data used...scrapflyScrapFly is a web scraping API with headless browser capabilities, pr...ScrapingAntScrapingAnt is a web scraping API with headless browser capabilities,...SingleStoreThe SingleStoreLoader allows you to load documents directly from a Si...SitemapExtends from the WebBaseLoader, SitemapLoader loads a sitemap from a ...SlackSlack is an instant messaging program.SnowflakeThis notebooks goes over how to load documents from SnowflakeSource CodeThis notebook covers how to load source code files using a special ap...SpiderSpider is the fastest and most affordable crawler and scraper that re...SpreedlySpreedly is a service that allows you to securely store credit cards ...StripeStripe is an Irish-American financial services and software as a serv...SubtitleThe SubRip file format is described on the Matroska multimedia contai...SurrealDBSurrealDB is an end-to-end cloud-native database designed for modern ...TelegramTelegram Messenger is a globally accessible freemium, cross-platform,...Tencent COS DirectoryTencent Cloud Object Storage (COS) is a distributedTencent COS FileTencent Cloud Object Storage (COS) is a distributedTensorFlow DatasetsTensorFlow Datasets is a collection of datasets ready to use, with Te...TiDBTiDB Cloud, is a comprehensive Database-as-a-Service (DBaaS) solution...2Markdown2markdown service transforms website content into structured markdown...TOMLTOML is a file format for configuration files. It is intended to be e...TrelloTrello is a web-based project management and collaboration tool that ...TSVA tab-separated values (TSV) file is a simple, text-based file format...TwitterTwitter is an online social media and social networking service.UnstructuredThis notebook covers how to use Unstructured document loader to load ...UnstructuredMarkdownLoaderThis notebook provides a quick overview for getting started with Unst...UnstructuredPDFLoaderUnstructured supports a common interface for working with unstructure...UpstageThis notebook covers how to get started with UpstageDocumentParseLoad...URLThis example covers how to load HTML documents from a list of URLs in...VsdxA visio file (with extension .vsdx) is associated with Microsoft Visi...WeatherOpenWeatherMap is an open-source weather service providerWebBaseLoaderThis covers how to use WebBaseLoader to load all text from HTML webpa...WhatsApp ChatWhatsApp (also called WhatsApp Messenger) is a freeware, cross-platfo...WikipediaWikipedia is a multilingual free online encyclopedia written and main...UnstructuredXMLLoaderThis notebook provides a quick overview for getting started with Unst...Xorbits Pandas DataFrameThis notebook goes over how to load data from a xorbits.pandas DataFr...YouTube audioBuilding chat or QA applications on YouTube videos is a topic of high...YouTube transcriptsYouTube is an online video sharing and social media platform created ...YoutubeLoaderDLLoader for Youtube leveraging the yt-dlp library.YuqueYuque is a professional cloud-based knowledge base for team collabora...ZeroxPDFLoaderZeroxPDFLoader is a document loader that leverages the Zerox library....Edit this pageWas this page helpful?PreviousZenGuard AINextDocument loadersWebpagesPDFsCloud ProvidersSocial PlatformsMessaging ServicesProductivity toolsCommon File TypesAll document loadersCommunityTwitterGitHubOrganizationPythonJS/TSMoreHomepageBlogYouTubeCopyright © 2025 LangChain, Inc.\\n\\n')]"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["## Web based loader\n", "from langchain_community.document_loaders import WebBaseLoader\n", "import bs4\n", "loader=WebBaseLoader(web_paths=(\"https://python.langchain.com/docs/integrations/document_loaders/\",),)\n", "docs=loader.load()\n", "docs"]}, {"cell_type": "code", "execution_count": 20, "id": "ca0f8216", "metadata": {}, "outputs": [{"data": {"text/plain": ["[Document(metadata={'source': 'https://lilianweng.github.io/posts/2023-06-23-agent/'}, page_content='\\n\\n      LLM Powered Autonomous Agents\\n    \\nDate: June 23, 2023  |  Estimated Reading Time: 31 min  |  Author: <PERSON><PERSON>\\n\\n\\nBuilding agents with LLM (large language model) as its core controller is a cool concept. Several proof-of-concepts demos, such as AutoGPT, GPT-Engineer and BabyAGI, serve as inspiring examples. The potentiality of LLM extends beyond generating well-written copies, stories, essays and programs; it can be framed as a powerful general problem solver.\\nAgent System Overview#\\nIn a LLM-powered autonomous agent system, LLM functions as the agent’s brain, complemented by several key components:\\n\\nPlanning\\n\\nSubgoal and decomposition: The agent breaks down large tasks into smaller, manageable subgoals, enabling efficient handling of complex tasks.\\nReflection and refinement: The agent can do self-criticism and self-reflection over past actions, learn from mistakes and refine them for future steps, thereby improving the quality of final results.\\n\\n\\nMemory\\n\\nShort-term memory: I would consider all the in-context learning (See Prompt Engineering) as utilizing short-term memory of the model to learn.\\nLong-term memory: This provides the agent with the capability to retain and recall (infinite) information over extended periods, often by leveraging an external vector store and fast retrieval.\\n\\n\\nTool use\\n\\nThe agent learns to call external APIs for extra information that is missing from the model weights (often hard to change after pre-training), including current information, code execution capability, access to proprietary information sources and more.\\n\\n\\n\\n\\n\\nOverview of a LLM-powered autonomous agent system.\\n\\nComponent One: Planning#\\nA complicated task usually involves many steps. An agent needs to know what they are and plan ahead.\\nTask Decomposition#\\nChain of thought (CoT; Wei et al. 2022) has become a standard prompting technique for enhancing model performance on complex tasks. The model is instructed to “think step by step” to utilize more test-time computation to decompose hard tasks into smaller and simpler steps. CoT transforms big tasks into multiple manageable tasks and shed lights into an interpretation of the model’s thinking process.\\nTree of Thoughts (Yao et al. 2023) extends CoT by exploring multiple reasoning possibilities at each step. It first decomposes the problem into multiple thought steps and generates multiple thoughts per step, creating a tree structure. The search process can be BFS (breadth-first search) or DFS (depth-first search) with each state evaluated by a classifier (via a prompt) or majority vote.\\nTask decomposition can be done (1) by LLM with simple prompting like \"Steps for XYZ.\\\\n1.\", \"What are the subgoals for achieving XYZ?\", (2) by using task-specific instructions; e.g. \"Write a story outline.\" for writing a novel, or (3) with human inputs.\\nAnother quite distinct approach, LLM+P (Liu et al. 2023), involves relying on an external classical planner to do long-horizon planning. This approach utilizes the Planning Domain Definition Language (PDDL) as an intermediate interface to describe the planning problem. In this process, LLM (1) translates the problem into “Problem PDDL”, then (2) requests a classical planner to generate a PDDL plan based on an existing “Domain PDDL”, and finally (3) translates the PDDL plan back into natural language. Essentially, the planning step is outsourced to an external tool, assuming the availability of domain-specific PDDL and a suitable planner which is common in certain robotic setups but not in many other domains.\\nSelf-Reflection#\\nSelf-reflection is a vital aspect that allows autonomous agents to improve iteratively by refining past action decisions and correcting previous mistakes. It plays a crucial role in real-world tasks where trial and error are inevitable.\\nReAct (Yao et al. 2023) integrates reasoning and acting within LLM by extending the action space to be a combination of task-specific discrete actions and the language space. The former enables LLM to interact with the environment (e.g. use Wikipedia search API), while the latter prompting LLM to generate reasoning traces in natural language.\\nThe ReAct prompt template incorporates explicit steps for LLM to think, roughly formatted as:\\nThought: ...\\nAction: ...\\nObservation: ...\\n... (Repeated many times)\\n\\n\\nExamples of reasoning trajectories for knowledge-intensive tasks (e.g. HotpotQA, FEVER) and decision-making tasks (e.g. AlfWorld Env, WebShop). (Image source: Yao et al. 2023).\\n\\nIn both experiments on knowledge-intensive tasks and decision-making tasks, ReAct works better than the Act-only baseline where Thought: … step is removed.\\nReflexion (Shinn & Labash 2023) is a framework to equip agents with dynamic memory and self-reflection capabilities to improve reasoning skills. Reflexion has a standard RL setup, in which the reward model provides a simple binary reward and the action space follows the setup in ReAct where the task-specific action space is augmented with language to enable complex reasoning steps. After each action $a_t$, the agent computes a heuristic $h_t$ and optionally may decide to reset the environment to start a new trial depending on the self-reflection results.\\n\\n\\nIllustration of the Reflexion framework. (Image source: Shinn & Labash, 2023)\\n\\nThe heuristic function determines when the trajectory is inefficient or contains hallucination and should be stopped. Inefficient planning refers to trajectories that take too long without success. Hallucination is defined as encountering a sequence of consecutive identical actions that lead to the same observation in the environment.\\nSelf-reflection is created by showing two-shot examples to LLM and each example is a pair of (failed trajectory, ideal reflection for guiding future changes in the plan). Then reflections are added into the agent’s working memory, up to three, to be used as context for querying LLM.\\n\\n\\nExperiments on AlfWorld Env and HotpotQA. Hallucination is a more common failure than inefficient planning in AlfWorld. (Image source: Shinn & Labash, 2023)\\n\\nChain of Hindsight (CoH; Liu et al. 2023) encourages the model to improve on its own outputs by explicitly presenting it with a sequence of past outputs, each annotated with feedback. Human feedback data is a collection of $D_h = \\\\{(x, y_i , r_i , z_i)\\\\}_{i=1}^n$, where $x$ is the prompt, each $y_i$ is a model completion, $r_i$ is the human rating of $y_i$, and $z_i$ is the corresponding human-provided hindsight feedback. Assume the feedback tuples are ranked by reward, $r_n \\\\geq r_{n-1} \\\\geq \\\\dots \\\\geq r_1$ The process is supervised fine-tuning where the data is a sequence in the form of $\\\\tau_h = (x, z_i, y_i, z_j, y_j, \\\\dots, z_n, y_n)$, where $\\\\leq i \\\\leq j \\\\leq n$. The model is finetuned to only predict $y_n$ where conditioned on the sequence prefix, such that the model can self-reflect to produce better output based on the feedback sequence. The model can optionally receive multiple rounds of instructions with human annotators at test time.\\nTo avoid overfitting, CoH adds a regularization term to maximize the log-likelihood of the pre-training dataset. To avoid shortcutting and copying (because there are many common words in feedback sequences), they randomly mask 0% - 5% of past tokens during training.\\nThe training dataset in their experiments is a combination of WebGPT comparisons, summarization from human feedback and human preference dataset.\\n\\n\\nAfter fine-tuning with CoH, the model can follow instructions to produce outputs with incremental improvement in a sequence. (Image source: Liu et al. 2023)\\n\\nThe idea of CoH is to present a history of sequentially improved outputs  in context and train the model to take on the trend to produce better outputs. Algorithm Distillation (AD; Laskin et al. 2023) applies the same idea to cross-episode trajectories in reinforcement learning tasks, where an algorithm is encapsulated in a long history-conditioned policy. Considering that an agent interacts with the environment many times and in each episode the agent gets a little better, AD concatenates this learning history and feeds that into the model. Hence we should expect the next predicted action to lead to better performance than previous trials. The goal is to learn the process of RL instead of training a task-specific policy itself.\\n\\n\\nIllustration of how Algorithm Distillation (AD) works. (Image source: Laskin et al. 2023).\\n\\nThe paper hypothesizes that any algorithm that generates a set of learning histories can be distilled into a neural network by performing behavioral cloning over actions. The history data is generated by a set of source policies, each trained for a specific task. At the training stage, during each RL run, a random task is sampled and a subsequence of multi-episode history is used for training, such that the learned policy is task-agnostic.\\nIn reality, the model has limited context window length, so episodes should be short enough to construct multi-episode history. Multi-episodic contexts of 2-4 episodes are necessary to learn a near-optimal in-context RL algorithm. The emergence of in-context RL requires long enough context.\\nIn comparison with three baselines, including ED (expert distillation, behavior cloning with expert trajectories instead of learning history), source policy (used for generating trajectories for distillation by UCB), RL^2 (Duan et al. 2017; used as upper bound since it needs online RL), AD demonstrates in-context RL with performance getting close to RL^2 despite only using offline RL and learns much faster than other baselines. When conditioned on partial training history of the source policy, AD also improves much faster than ED baseline.\\n\\n\\nComparison of AD, ED, source policy and RL^2 on environments that require memory and exploration. Only binary reward is assigned. The source policies are trained with A3C for \"dark\" environments and DQN for watermaze.(Image source: Laskin et al. 2023)\\n\\nComponent Two: Memory#\\n(Big thank you to ChatGPT for helping me draft this section. I’ve learned a lot about the human brain and data structure for fast MIPS in my conversations with ChatGPT.)\\nTypes of Memory#\\nMemory can be defined as the processes used to acquire, store, retain, and later retrieve information. There are several types of memory in human brains.\\n\\n\\nSensory Memory: This is the earliest stage of memory, providing the ability to retain impressions of sensory information (visual, auditory, etc) after the original stimuli have ended. Sensory memory typically only lasts for up to a few seconds. Subcategories include iconic memory (visual), echoic memory (auditory), and haptic memory (touch).\\n\\n\\nShort-Term Memory (STM) or Working Memory: It stores information that we are currently aware of and needed to carry out complex cognitive tasks such as learning and reasoning. Short-term memory is believed to have the capacity of about 7 items (Miller 1956) and lasts for 20-30 seconds.\\n\\n\\nLong-Term Memory (LTM): Long-term memory can store information for a remarkably long time, ranging from a few days to decades, with an essentially unlimited storage capacity. There are two subtypes of LTM:\\n\\nExplicit / declarative memory: This is memory of facts and events, and refers to those memories that can be consciously recalled, including episodic memory (events and experiences) and semantic memory (facts and concepts).\\nImplicit / procedural memory: This type of memory is unconscious and involves skills and routines that are performed automatically, like riding a bike or typing on a keyboard.\\n\\n\\n\\n\\n\\nCategorization of human memory.\\n\\nWe can roughly consider the following mappings:\\n\\nSensory memory as learning embedding representations for raw inputs, including text, image or other modalities;\\nShort-term memory as in-context learning. It is short and finite, as it is restricted by the finite context window length of Transformer.\\nLong-term memory as the external vector store that the agent can attend to at query time, accessible via fast retrieval.\\n\\nMaximum Inner Product Search (MIPS)#\\nThe external memory can alleviate the restriction of finite attention span.  A standard practice is to save the embedding representation of information into a vector store database that can support fast maximum inner-product search (MIPS). To optimize the retrieval speed, the common choice is the approximate nearest neighbors (ANN)\\u200b algorithm to return approximately top k nearest neighbors to trade off a little accuracy lost for a huge speedup.\\nA couple common choices of ANN algorithms for fast MIPS:\\n\\nLSH (Locality-Sensitive Hashing): It introduces a hashing function such that similar input items are mapped to the same buckets with high probability, where the number of buckets is much smaller than the number of inputs.\\nANNOY (Approximate Nearest Neighbors Oh Yeah): The core data structure are random projection trees, a set of binary trees where each non-leaf node represents a hyperplane splitting the input space into half and each leaf stores one data point. Trees are built independently and at random, so to some extent, it mimics a hashing function. ANNOY search happens in all the trees to iteratively search through the half that is closest to the query and then aggregates the results. The idea is quite related to KD tree but a lot more scalable.\\nHNSW (Hierarchical Navigable Small World): It is inspired by the idea of small world networks where most nodes can be reached by any other nodes within a small number of steps; e.g. “six degrees of separation” feature of social networks. HNSW builds hierarchical layers of these small-world graphs, where the bottom layers contain the actual data points. The layers in the middle create shortcuts to speed up search. When performing a search, HNSW starts from a random node in the top layer and navigates towards the target. When it can’t get any closer, it moves down to the next layer, until it reaches the bottom layer. Each move in the upper layers can potentially cover a large distance in the data space, and each move in the lower layers refines the search quality.\\nFAISS (Facebook AI Similarity Search): It operates on the assumption that in high dimensional space, distances between nodes follow a Gaussian distribution and thus there should exist clustering of data points. FAISS applies vector quantization by partitioning the vector space into clusters and then refining the quantization within clusters. Search first looks for cluster candidates with coarse quantization and then further looks into each cluster with finer quantization.\\nScaNN (Scalable Nearest Neighbors): The main innovation in ScaNN is anisotropic vector quantization. It quantizes a data point $x_i$ to $\\\\tilde{x}_i$ such that the inner product $\\\\langle q, x_i \\\\rangle$ is as similar to the original distance of $\\\\angle q, \\\\tilde{x}_i$ as possible, instead of picking the closet quantization centroid points.\\n\\n\\n\\nComparison of MIPS algorithms, measured in recall@10. (Image source: Google Blog, 2020)\\n\\nCheck more MIPS algorithms and performance comparison in ann-benchmarks.com.\\nComponent Three: Tool Use#\\nTool use is a remarkable and distinguishing characteristic of human beings. We create, modify and utilize external objects to do things that go beyond our physical and cognitive limits. Equipping LLMs with external tools can significantly extend the model capabilities.\\n\\n\\nA picture of a sea otter using rock to crack open a seashell, while floating in the water. While some other animals can use tools, the complexity is not comparable with humans. (Image source: Animals using tools)\\n\\nMRKL (Karpas et al. 2022), short for “Modular Reasoning, Knowledge and Language”, is a neuro-symbolic architecture for autonomous agents. A MRKL system is proposed to contain a collection of “expert” modules and the general-purpose LLM works as a router to route inquiries to the best suitable expert module. These modules can be neural (e.g. deep learning models) or symbolic (e.g. math calculator, currency converter, weather API).\\nThey did an experiment on fine-tuning LLM to call a calculator, using arithmetic as a test case. Their experiments showed that it was harder to solve verbal math problems than explicitly stated math problems because LLMs (7B Jurassic1-large model) failed to extract the right arguments for the basic arithmetic reliably. The results highlight when the external symbolic tools can work reliably, knowing when to and how to use the tools are crucial, determined by the LLM capability.\\nBoth TALM (Tool Augmented Language Models; Parisi et al. 2022) and Toolformer (Schick et al. 2023) fine-tune a LM to learn to use external tool APIs. The dataset is expanded based on whether a newly added API call annotation can improve the quality of model outputs. See more details in the “External APIs” section of Prompt Engineering.\\nChatGPT Plugins and OpenAI API  function calling are good examples of LLMs augmented with tool use capability working in practice. The collection of tool APIs can be provided by other developers (as in Plugins) or self-defined (as in function calls).\\nHuggingGPT (Shen et al. 2023) is a framework to use ChatGPT as the task planner to select models available in HuggingFace platform according to the model descriptions and summarize the response based on the execution results.\\n\\n\\nIllustration of how HuggingGPT works. (Image source: Shen et al. 2023)\\n\\nThe system comprises of 4 stages:\\n(1) Task planning: LLM works as the brain and parses the user requests into multiple tasks. There are four attributes associated with each task: task type, ID, dependencies, and arguments. They use few-shot examples to guide LLM to do task parsing and planning.\\nInstruction:\\n\\nThe AI assistant can parse user input to several tasks: [{\"task\": task, \"id\", task_id, \"dep\": dependency_task_ids, \"args\": {\"text\": text, \"image\": URL, \"audio\": URL, \"video\": URL}}]. The \"dep\" field denotes the id of the previous task which generates a new resource that the current task relies on. A special tag \"-task_id\" refers to the generated text image, audio and video in the dependency task with id as task_id. The task MUST be selected from the following options: {{ Available Task List }}. There is a logical relationship between tasks, please note their order. If the user input can\\'t be parsed, you need to reply empty JSON. Here are several cases for your reference: {{ Demonstrations }}. The chat history is recorded as {{ Chat History }}. From this chat history, you can find the path of the user-mentioned resources for your task planning.\\n\\n(2) Model selection: LLM distributes the tasks to expert models, where the request is framed as a multiple-choice question. LLM is presented with a list of models to choose from. Due to the limited context length, task type based filtration is needed.\\nInstruction:\\n\\nGiven the user request and the call command, the AI assistant helps the user to select a suitable model from a list of models to process the user request. The AI assistant merely outputs the model id of the most appropriate model. The output must be in a strict JSON format: \"id\": \"id\", \"reason\": \"your detail reason for the choice\". We have a list of models for you to choose from {{ Candidate Models }}. Please select one model from the list.\\n\\n(3) Task execution: Expert models execute on the specific tasks and log results.\\nInstruction:\\n\\nWith the input and the inference results, the AI assistant needs to describe the process and results. The previous stages can be formed as - User Input: {{ User Input }}, Task Planning: {{ Tasks }}, Model Selection: {{ Model Assignment }}, Task Execution: {{ Predictions }}. You must first answer the user\\'s request in a straightforward manner. Then describe the task process and show your analysis and model inference results to the user in the first person. If inference results contain a file path, must tell the user the complete file path.\\n\\n(4) Response generation: LLM receives the execution results and provides summarized results to users.\\nTo put HuggingGPT into real world usage, a couple challenges need to solve: (1) Efficiency improvement is needed as both LLM inference rounds and interactions with other models slow down the process; (2) It relies on a long context window to communicate over complicated task content; (3) Stability improvement of LLM outputs and external model services.\\nAPI-Bank (Li et al. 2023) is a benchmark for evaluating the performance of tool-augmented LLMs. It contains 53 commonly used API tools, a complete tool-augmented LLM workflow, and 264 annotated dialogues that involve 568 API calls. The selection of APIs is quite diverse, including search engines, calculator, calendar queries, smart home control, schedule management, health data management, account authentication workflow and more. Because there are a large number of APIs, LLM first has access to API search engine to find the right API to call and then uses the corresponding documentation to make a call.\\n\\n\\nPseudo code of how LLM makes an API call in API-Bank. (Image source: Li et al. 2023)\\n\\nIn the API-Bank workflow, LLMs need to make a couple of decisions and at each step we can evaluate how accurate that decision is. Decisions include:\\n\\nWhether an API call is needed.\\nIdentify the right API to call: if not good enough, LLMs need to iteratively modify the API inputs (e.g. deciding search keywords for Search Engine API).\\nResponse based on the API results: the model can choose to refine and call again if results are not satisfied.\\n\\nThis benchmark evaluates the agent’s tool use capabilities at three levels:\\n\\nLevel-1 evaluates the ability to call the API. Given an API’s description, the model needs to determine whether to call a given API, call it correctly, and respond properly to API returns.\\nLevel-2 examines the ability to retrieve the API. The model needs to search for possible APIs that may solve the user’s requirement and learn how to use them by reading documentation.\\nLevel-3 assesses the ability to plan API beyond retrieve and call. Given unclear user requests (e.g. schedule group meetings, book flight/hotel/restaurant for a trip), the model may have to conduct multiple API calls to solve it.\\n\\nCase Studies#\\nScientific Discovery Agent#\\nChemCrow (Bran et al. 2023) is a domain-specific example in which LLM is augmented with 13 expert-designed tools to accomplish tasks across organic synthesis, drug discovery, and materials design. The workflow, implemented in LangChain, reflects what was previously described in the ReAct and MRKLs and combines CoT reasoning with tools relevant to the tasks:\\n\\nThe LLM is provided with a list of tool names, descriptions of their utility, and details about the expected input/output.\\nIt is then instructed to answer a user-given prompt using the tools provided when necessary. The instruction suggests the model to follow the ReAct format - Thought, Action, Action Input, Observation.\\n\\nOne interesting observation is that while the LLM-based evaluation concluded that GPT-4 and ChemCrow perform nearly equivalently, human evaluations with experts oriented towards the completion and chemical correctness of the solutions showed that ChemCrow outperforms GPT-4 by a large margin. This indicates a potential problem with using LLM to evaluate its own performance on domains that requires deep expertise. The lack of expertise may cause LLMs not knowing its flaws and thus cannot well judge the correctness of task results.\\nBoiko et al. (2023) also looked into LLM-empowered agents for scientific discovery, to handle autonomous design, planning, and performance of complex scientific experiments. This agent can use tools to browse the Internet, read documentation, execute code, call robotics experimentation APIs and leverage other LLMs.\\nFor example, when requested to \"develop a novel anticancer drug\", the model came up with the following reasoning steps:\\n\\ninquired about current trends in anticancer drug discovery;\\nselected a target;\\nrequested a scaffold targeting these compounds;\\nOnce the compound was identified, the model attempted its synthesis.\\n\\nThey also discussed the risks, especially with illicit drugs and bioweapons. They developed a test set containing a list of known chemical weapon agents and asked the agent to synthesize them. 4 out of 11 requests (36%) were accepted to obtain a synthesis solution and the agent attempted to consult documentation to execute the procedure. 7 out of 11 were rejected and among these 7 rejected cases, 5 happened after a Web search while 2 were rejected based on prompt only.\\nGenerative Agents Simulation#\\nGenerative Agents (Park, et al. 2023) is super fun experiment where 25 virtual characters, each controlled by a LLM-powered agent, are living and interacting in a sandbox environment, inspired by The Sims. Generative agents create believable simulacra of human behavior for interactive applications.\\nThe design of generative agents combines LLM with memory, planning and reflection mechanisms to enable agents to behave conditioned on past experience, as well as to interact with other agents.\\n\\nMemory stream: is a long-term memory module (external database) that records a comprehensive list of agents’ experience in natural language.\\n\\nEach element is an observation, an event directly provided by the agent.\\n- Inter-agent communication can trigger new natural language statements.\\n\\n\\nRetrieval model: surfaces the context to inform the agent’s behavior, according to relevance, recency and importance.\\n\\nRecency: recent events have higher scores\\nImportance: distinguish mundane from core memories. Ask LM directly.\\nRelevance: based on how related it is to the current situation / query.\\n\\n\\nReflection mechanism: synthesizes memories into higher level inferences over time and guides the agent’s future behavior. They are higher-level summaries of past events (<- note that this is a bit different from self-reflection above)\\n\\nPrompt LM with 100 most recent observations and to generate 3 most salient high-level questions given a set of observations/statements. Then ask LM to answer those questions.\\n\\n\\nPlanning & Reacting: translate the reflections and the environment information into actions\\n\\nPlanning is essentially in order to optimize believability at the moment vs in time.\\nPrompt template: {Intro of an agent X}. Here is X\\'s plan today in broad strokes: 1)\\nRelationships between agents and observations of one agent by another are all taken into consideration for planning and reacting.\\nEnvironment information is present in a tree structure.\\n\\n\\n\\n\\n\\nThe generative agent architecture. (Image source: Park et al. 2023)\\n\\nThis fun simulation results in emergent social behavior, such as information diffusion, relationship memory (e.g. two agents continuing the conversation topic) and coordination of social events (e.g. host a party and invite many others).\\nProof-of-Concept Examples#\\nAutoGPT has drawn a lot of attention into the possibility of setting up autonomous agents with LLM as the main controller. It has quite a lot of reliability issues given the natural language interface, but nevertheless a cool proof-of-concept demo. A lot of code in AutoGPT is about format parsing.\\nHere is the system message used by AutoGPT, where {{...}} are user inputs:\\nYou are {{ai-name}}, {{user-provided AI bot description}}.\\nYour decisions must always be made independently without seeking user assistance. Play to your strengths as an LLM and pursue simple strategies with no legal complications.\\n\\nGOALS:\\n\\n1. {{user-provided goal 1}}\\n2. {{user-provided goal 2}}\\n3. ...\\n4. ...\\n5. ...\\n\\nConstraints:\\n1. ~4000 word limit for short term memory. Your short term memory is short, so immediately save important information to files.\\n2. If you are unsure how you previously did something or want to recall past events, thinking about similar events will help you remember.\\n3. No user assistance\\n4. Exclusively use the commands listed in double quotes e.g. \"command name\"\\n5. Use subprocesses for commands that will not terminate within a few minutes\\n\\nCommands:\\n1. Google Search: \"google\", args: \"input\": \"<search>\"\\n2. Browse Website: \"browse_website\", args: \"url\": \"<url>\", \"question\": \"<what_you_want_to_find_on_website>\"\\n3. Start GPT Agent: \"start_agent\", args: \"name\": \"<name>\", \"task\": \"<short_task_desc>\", \"prompt\": \"<prompt>\"\\n4. Message GPT Agent: \"message_agent\", args: \"key\": \"<key>\", \"message\": \"<message>\"\\n5. List GPT Agents: \"list_agents\", args:\\n6. Delete GPT Agent: \"delete_agent\", args: \"key\": \"<key>\"\\n7. Clone Repository: \"clone_repository\", args: \"repository_url\": \"<url>\", \"clone_path\": \"<directory>\"\\n8. Write to file: \"write_to_file\", args: \"file\": \"<file>\", \"text\": \"<text>\"\\n9. Read file: \"read_file\", args: \"file\": \"<file>\"\\n10. Append to file: \"append_to_file\", args: \"file\": \"<file>\", \"text\": \"<text>\"\\n11. Delete file: \"delete_file\", args: \"file\": \"<file>\"\\n12. Search Files: \"search_files\", args: \"directory\": \"<directory>\"\\n13. Analyze Code: \"analyze_code\", args: \"code\": \"<full_code_string>\"\\n14. Get Improved Code: \"improve_code\", args: \"suggestions\": \"<list_of_suggestions>\", \"code\": \"<full_code_string>\"\\n15. Write Tests: \"write_tests\", args: \"code\": \"<full_code_string>\", \"focus\": \"<list_of_focus_areas>\"\\n16. Execute Python File: \"execute_python_file\", args: \"file\": \"<file>\"\\n17. Generate Image: \"generate_image\", args: \"prompt\": \"<prompt>\"\\n18. Send Tweet: \"send_tweet\", args: \"text\": \"<text>\"\\n19. Do Nothing: \"do_nothing\", args:\\n20. Task Complete (Shutdown): \"task_complete\", args: \"reason\": \"<reason>\"\\n\\nResources:\\n1. Internet access for searches and information gathering.\\n2. Long Term memory management.\\n3. GPT-3.5 powered Agents for delegation of simple tasks.\\n4. File output.\\n\\nPerformance Evaluation:\\n1. Continuously review and analyze your actions to ensure you are performing to the best of your abilities.\\n2. Constructively self-criticize your big-picture behavior constantly.\\n3. Reflect on past decisions and strategies to refine your approach.\\n4. Every command has a cost, so be smart and efficient. Aim to complete tasks in the least number of steps.\\n\\nYou should only respond in JSON format as described below\\nResponse Format:\\n{\\n    \"thoughts\": {\\n        \"text\": \"thought\",\\n        \"reasoning\": \"reasoning\",\\n        \"plan\": \"- short bulleted\\\\n- list that conveys\\\\n- long-term plan\",\\n        \"criticism\": \"constructive self-criticism\",\\n        \"speak\": \"thoughts summary to say to user\"\\n    },\\n    \"command\": {\\n        \"name\": \"command name\",\\n        \"args\": {\\n            \"arg name\": \"value\"\\n        }\\n    }\\n}\\nEnsure the response can be parsed by Python json.loads\\nGPT-Engineer is another project to create a whole repository of code given a task specified in natural language. The GPT-Engineer is instructed to think over a list of smaller components to build and ask for user input to clarify questions as needed.\\nHere are a sample conversation for task clarification sent to OpenAI ChatCompletion endpoint used by GPT-Engineer. The user inputs are wrapped in {{user input text}}.\\n[\\n  {\\n    \"role\": \"system\",\\n    \"content\": \"You will read instructions and not carry them out, only seek to clarify them.\\\\nSpecifically you will first summarise a list of super short bullets of areas that need clarification.\\\\nThen you will pick one clarifying question, and wait for an answer from the user.\\\\n\"\\n  },\\n  {\\n    \"role\": \"user\",\\n    \"content\": \"We are writing {{a Super Mario game in python. MVC components split in separate files. Keyboard control.}}\\\\n\"\\n  },\\n  {\\n    \"role\": \"assistant\",\\n    \"content\": \"Summary of areas that need clarification:\\\\n1. Specifics of the Super Mario game (e.g. level design, characters, gameplay mechanics)\\\\n2. Details about the MVC components (e.g. which components are in each file)\\\\n3. Keyboard control implementation (e.g. which keys to use, how to handle input)\\\\n\\\\nClarifying question:\\\\nCan you provide more details about the Super Mario game, such as level design, characters, and gameplay mechanics?\"\\n  },\\n  {\\n    \"role\": \"user\",\\n    \"content\": \"{{There are 10 levels in total. The main character is a plumber named Mario, who can walk and jump. It is a classical platform game just like Super Mario. The main character moves from left to right, trying to get to the destination, where there are many obstacles and attacks from enemies in the process.}}\\\\n\\\\nIs anything else unclear? If yes, only answer in the form:\\\\n{remaining unclear areas} remaining questions.\\\\n{Next question}\\\\nIf everything is sufficiently clear, only answer \\\\\"Nothing more to clarify.\\\\\".\"\\n  },\\n  {\\n    \"role\": \"assistant\",\\n    \"content\": \"Remaining unclear areas: 2 remaining questions.\\\\nCan you provide more information about how the MVC components are split into separate files?\"\\n  },\\n  {\\n    \"role\": \"user\",\\n    \"content\": \"{{Make your own assumptions and state them explicitly before starting}}\"\\n  }\\n]\\nThen after these clarification, the agent moved into the code writing mode with a different system message.\\nSystem message:\\n\\nYou will get instructions for code to write.\\nYou will write a very long answer. Make sure that every detail of the architecture is, in the end, implemented as code.\\nMake sure that every detail of the architecture is, in the end, implemented as code.\\nThink step by step and reason yourself to the right decisions to make sure we get it right.\\nYou will first lay out the names of the core classes, functions, methods that will be necessary, as well as a quick comment on their purpose.\\nThen you will output the content of each file including ALL code.\\nEach file must strictly follow a markdown code block format, where the following tokens must be replaced such that\\nFILENAME is the lowercase file name including the file extension,\\nLANG is the markup code block language for the code’s language, and CODE is the code:\\nFILENAME\\nCODE\\nYou will start with the “entrypoint” file, then go to the ones that are imported by that file, and so on.\\nPlease note that the code should be fully functional. No placeholders.\\nFollow a language and framework appropriate best practice file naming convention.\\nMake sure that files contain all imports, types etc. Make sure that code in different files are compatible with each other.\\nEnsure to implement all code, if you are unsure, write a plausible implementation.\\nInclude module dependency or package manager dependency definition file.\\nBefore you finish, double check that all parts of the architecture is present in the files.\\nUseful to know:\\nYou almost always put different classes in different files.\\nFor Python, you always create an appropriate requirements.txt file.\\nFor NodeJS, you always create an appropriate package.json file.\\nYou always add a comment briefly describing the purpose of the function definition.\\nYou try to add comments explaining very complex bits of logic.\\nYou always follow the best practices for the requested languages in terms of describing the code written as a defined\\npackage/project.\\nPython toolbelt preferences:\\n\\npytest\\ndataclasses\\n\\n\\nConversatin samples:\\n[\\n  {\\n    \"role\": \"system\",\\n    \"content\": \"You will get instructions for code to write.\\\\nYou will write a very long answer. Make sure that every detail of the architecture is, in the end, implemented as code.\\\\nMake sure that every detail of the architecture is, in the end, implemented as code.\\\\n\\\\nThink step by step and reason yourself to the right decisions to make sure we get it right.\\\\nYou will first lay out the names of the core classes, functions, methods that will be necessary, as well as a quick comment on their purpose.\\\\n\\\\nThen you will output the content of each file including ALL code.\\\\nEach file must strictly follow a markdown code block format, where the following tokens must be replaced such that\\\\nFILENAME is the lowercase file name including the file extension,\\\\nLANG is the markup code block language for the code\\'s language, and CODE is the code:\\\\n\\\\nFILENAME\\\\n```LANG\\\\nCODE\\\\n```\\\\n\\\\nYou will start with the \\\\\"entrypoint\\\\\" file, then go to the ones that are imported by that file, and so on.\\\\nPlease note that the code should be fully functional. No placeholders.\\\\n\\\\nFollow a language and framework appropriate best practice file naming convention.\\\\nMake sure that files contain all imports, types etc. Make sure that code in different files are compatible with each other.\\\\nEnsure to implement all code, if you are unsure, write a plausible implementation.\\\\nInclude module dependency or package manager dependency definition file.\\\\nBefore you finish, double check that all parts of the architecture is present in the files.\\\\n\\\\nUseful to know:\\\\nYou almost always put different classes in different files.\\\\nFor Python, you always create an appropriate requirements.txt file.\\\\nFor NodeJS, you always create an appropriate package.json file.\\\\nYou always add a comment briefly describing the purpose of the function definition.\\\\nYou try to add comments explaining very complex bits of logic.\\\\nYou always follow the best practices for the requested languages in terms of describing the code written as a defined\\\\npackage/project.\\\\n\\\\n\\\\nPython toolbelt preferences:\\\\n- pytest\\\\n- dataclasses\\\\n\"\\n  },\\n #  … same conversation as earlier, ended with \"Make your own assumptions and state them explicitly before starting\".\\n  {\\n    \"role\": \"assistant\",\\n    \"content\": \"Assumptions:\\\\n1. Model: The model will contain the game\\'s data, such as level information, character states, and enemy positions.\\\\n2. View: The view will handle the game\\'s visuals, including rendering the game objects, backgrounds, and updating the display.\\\\n3. Controller: The controller will manage user input, such as keyboard controls, and update the model accordingly.\\\\n\\\\nWith these assumptions in mind, is there any specific aspect of the keyboard control implementation you would like me to clarify?\"\\n  },\\n  {\\n    \"role\": \"user\",\\n    \"content\": \"Please now remember the steps:\\\\n\\\\nThink step by step and reason yourself to the right decisions to make sure we get it right.\\\\nFirst lay out the names of the core classes, functions, methods that will be necessary, As well as a quick comment on their purpose.\\\\n\\\\nThen you will output the content of each file including ALL code.\\\\nEach file must strictly follow a markdown code block format, where the following tokens must be replaced such that\\\\nFILENAME is the lowercase file name including the file extension,\\\\nLANG is the markup code block language for the code\\'s language, and CODE is the code:\\\\n\\\\nFILENAME\\\\n```LANG\\\\nCODE\\\\n```\\\\n\\\\nPlease note that the code should be fully functional. No placeholders.\\\\n\\\\nYou will start with the \\\\\"entrypoint\\\\\" file, then go to the ones that are imported by that file, and so on.\\\\nFollow a language and framework appropriate best practice file naming convention.\\\\nMake sure that files contain all imports, types etc. The code should be fully functional. Make sure that code in different files are compatible with each other.\\\\nBefore you finish, double check that all parts of the architecture is present in the files.\\\\n\"\\n  }\\n]\\nChallenges#\\nAfter going through key ideas and demos of building LLM-centered agents, I start to see a couple common limitations:\\n\\n\\nFinite context length: The restricted context capacity limits the inclusion of historical information, detailed instructions, API call context, and responses. The design of the system has to work with this limited communication bandwidth, while mechanisms like self-reflection to learn from past mistakes would benefit a lot from long or infinite context windows. Although vector stores and retrieval can provide access to a larger knowledge pool, their representation power is not as powerful as full attention.\\n\\n\\nChallenges in long-term planning and task decomposition: Planning over a lengthy history and effectively exploring the solution space remain challenging. LLMs struggle to adjust plans when faced with unexpected errors, making them less robust compared to humans who learn from trial and error.\\n\\n\\nReliability of natural language interface: Current agent system relies on natural language as an interface between LLMs and external components such as memory and tools. However, the reliability of model outputs is questionable, as LLMs may make formatting errors and occasionally exhibit rebellious behavior (e.g. refuse to follow an instruction). Consequently, much of the agent demo code focuses on parsing model output.\\n\\n\\nCitation#\\nCited as:\\n\\nWeng, Lilian. (Jun 2023). “LLM-powered Autonomous Agents”. Lil’Log. https://lilianweng.github.io/posts/2023-06-23-agent/.\\n\\nOr\\n@article{weng2023agent,\\n  title   = \"LLM-powered Autonomous Agents\",\\n  author  = \"Weng, Lilian\",\\n  journal = \"lilianweng.github.io\",\\n  year    = \"2023\",\\n  month   = \"Jun\",\\n  url     = \"https://lilianweng.github.io/posts/2023-06-23-agent/\"\\n}\\nReferences#\\n[1] Wei et al. “Chain of thought prompting elicits reasoning in large language models.” NeurIPS 2022\\n[2] Yao et al. “Tree of Thoughts: Dliberate Problem Solving with Large Language Models.” arXiv preprint arXiv:2305.10601 (2023).\\n[3] Liu et al. “Chain of Hindsight Aligns Language Models with Feedback\\n“ arXiv preprint arXiv:2302.02676 (2023).\\n[4] Liu et al. “LLM+P: Empowering Large Language Models with Optimal Planning Proficiency” arXiv preprint arXiv:2304.11477 (2023).\\n[5] Yao et al. “ReAct: Synergizing reasoning and acting in language models.” ICLR 2023.\\n[6] Google Blog. “Announcing ScaNN: Efficient Vector Similarity Search” July 28, 2020.\\n[7] https://chat.openai.com/share/46ff149e-a4c7-4dd7-a800-fc4a642ea389\\n[8] Shinn & Labash. “Reflexion: an autonomous agent with dynamic memory and self-reflection” arXiv preprint arXiv:2303.11366 (2023).\\n[9] Laskin et al. “In-context Reinforcement Learning with Algorithm Distillation” ICLR 2023.\\n[10] Karpas et al. “MRKL Systems A modular, neuro-symbolic architecture that combines large language models, external knowledge sources and discrete reasoning.” arXiv preprint arXiv:2205.00445 (2022).\\n[11] Nakano et al. “Webgpt: Browser-assisted question-answering with human feedback.” arXiv preprint arXiv:2112.09332 (2021).\\n[12] Parisi et al. “TALM: Tool Augmented Language Models”\\n[13] Schick et al. “Toolformer: Language Models Can Teach Themselves to Use Tools.” arXiv preprint arXiv:2302.04761 (2023).\\n[14] Weaviate Blog. Why is Vector Search so fast? Sep 13, 2022.\\n[15] Li et al. “API-Bank: A Benchmark for Tool-Augmented LLMs” arXiv preprint arXiv:2304.08244 (2023).\\n[16] Shen et al. “HuggingGPT: Solving AI Tasks with ChatGPT and its Friends in HuggingFace” arXiv preprint arXiv:2303.17580 (2023).\\n[17] Bran et al. “ChemCrow: Augmenting large-language models with chemistry tools.” arXiv preprint arXiv:2304.05376 (2023).\\n[18] Boiko et al. “Emergent autonomous scientific research capabilities of large language models.” arXiv preprint arXiv:2304.05332 (2023).\\n[19] Joon Sung Park, et al. “Generative Agents: Interactive Simulacra of Human Behavior.” arXiv preprint arXiv:2304.03442 (2023).\\n[20] AutoGPT. https://github.com/Significant-Gravitas/Auto-GPT\\n[21] GPT-Engineer. https://github.com/AntonOsika/gpt-engineer\\n')]"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["## Web based loader\n", "from langchain_community.document_loaders import WebBaseLoader\n", "import bs4\n", "loader=WebBaseLoader(web_paths=(\"https://lilianweng.github.io/posts/2023-06-23-agent/\",),\n", "                     bs_kwargs=dict(parse_only=bs4.SoupStrainer(\n", "                         class_=(\"post-title\",\"post-content\",\"post-header\")\n", "                     ))\n", "                     )\n", "\n", "doc=loader.load()\n", "doc"]}, {"cell_type": "code", "execution_count": 21, "id": "4b23ba7f", "metadata": {}, "outputs": [{"data": {"text/plain": ["[Document(metadata={'Published': '2023-08-02', 'Title': 'Attention Is All You Need', 'Authors': '<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>', 'Summary': 'The dominant sequence transduction models are based on complex recurrent or\\nconvolutional neural networks in an encoder-decoder configuration. The best\\nperforming models also connect the encoder and decoder through an attention\\nmechanism. We propose a new simple network architecture, the Transformer, based\\nsolely on attention mechanisms, dispensing with recurrence and convolutions\\nentirely. Experiments on two machine translation tasks show these models to be\\nsuperior in quality while being more parallelizable and requiring significantly\\nless time to train. Our model achieves 28.4 BLEU on the WMT 2014\\nEnglish-to-German translation task, improving over the existing best results,\\nincluding ensembles by over 2 BLEU. On the WMT 2014 English-to-French\\ntranslation task, our model establishes a new single-model state-of-the-art\\nBLEU score of 41.8 after training for 3.5 days on eight GPUs, a small fraction\\nof the training costs of the best models from the literature. We show that the\\nTransformer generalizes well to other tasks by applying it successfully to\\nEnglish constituency parsing both with large and limited training data.'}, page_content='Provided proper attribution is provided, Google hereby grants permission to\\nreproduce the tables and figures in this paper solely for use in journalistic or\\nscholarly works.\\nAttention Is All You Need\\nAshish Vaswani∗\\nGoogle Brain\\<EMAIL>\\nNoam Shazeer∗\\nGoogle Brain\\<EMAIL>\\nNiki Parmar∗\\nGoogle Research\\<EMAIL>\\nJakob Uszkoreit∗\\nGoogle Research\\<EMAIL>\\nLlion Jones∗\\nGoogle Research\\<EMAIL>\\nAidan N. Gomez∗†\\nUniversity of Toronto\\<EMAIL>\\nŁukasz Kaiser∗\\nGoogle Brain\\<EMAIL>\\nIllia Polosukhin∗‡\\<EMAIL>\\nAbstract\\nThe dominant sequence transduction models are based on complex recurrent or\\nconvolutional neural networks that include an encoder and a decoder. The best\\nperforming models also connect the encoder and decoder through an attention\\nmechanism. We propose a new simple network architecture, the Transformer,\\nbased solely on attention mechanisms, dispensing with recurrence and convolutions\\nentirely. Experiments on two machine translation tasks show these models to\\nbe superior in quality while being more parallelizable and requiring significantly\\nless time to train. Our model achieves 28.4 BLEU on the WMT 2014 English-\\nto-German translation task, improving over the existing best results, including\\nensembles, by over 2 BLEU. On the WMT 2014 English-to-French translation task,\\nour model establishes a new single-model state-of-the-art BLEU score of 41.8 after\\ntraining for 3.5 days on eight GPUs, a small fraction of the training costs of the\\nbest models from the literature. We show that the Transformer generalizes well to\\nother tasks by applying it successfully to English constituency parsing both with\\nlarge and limited training data.\\n∗Equal contribution. Listing order is random. Jakob proposed replacing RNNs with self-attention and started\\nthe effort to evaluate this idea. Ashish, with Illia, designed and implemented the first Transformer models and\\nhas been crucially involved in every aspect of this work. Noam proposed scaled dot-product attention, multi-head\\nattention and the parameter-free position representation and became the other person involved in nearly every\\ndetail. Niki designed, implemented, tuned and evaluated countless model variants in our original codebase and\\ntensor2tensor. Llion also experimented with novel model variants, was responsible for our initial codebase, and\\nefficient inference and visualizations. Lukasz and Aidan spent countless long days designing various parts of and\\nimplementing tensor2tensor, replacing our earlier codebase, greatly improving results and massively accelerating\\nour research.\\n†Work performed while at Google Brain.\\n‡Work performed while at Google Research.\\n31st Conference on Neural Information Processing Systems (NIPS 2017), Long Beach, CA, USA.\\narXiv:1706.03762v7  [cs.CL]  2 Aug 2023\\n1\\nIntroduction\\nRecurrent neural networks, long short-term memory [13] and gated recurrent [7] neural networks\\nin particular, have been firmly established as state of the art approaches in sequence modeling and\\ntransduction problems such as language modeling and machine translation [35, 2, 5]. Numerous\\nefforts have since continued to push the boundaries of recurrent language models and encoder-decoder\\narchitectures [38, 24, 15].\\nRecurrent models typically factor computation along the symbol positions of the input and output\\nsequences. Aligning the positions to steps in computation time, they generate a sequence of hidden\\nstates ht, as a function of the previous hidden state ht−1 and the input for position t. This inherently\\nsequential nature precludes parallelization within training examples, which becomes critical at longer\\nsequence lengths, as memory constraints limit batching across examples. Recent work has achieved\\nsignificant improvements in computational efficiency through factorization tricks [21] and conditional\\ncomputation [32], while also improving model performance in case of the latter. The fundamental\\nconstraint of sequential computation, however, remains.\\nAttention mechanisms have become an integral part of compelling sequence modeling and transduc-\\ntion models in various tasks, allowing modeling of dependencies without regard to their distance in\\nthe input or output sequences [2, 19]. In all but a few cases [27], however, such attention mechanisms\\nare used in conjunction with a recurrent network.\\nIn this work we propose the Transformer, a model architecture eschewing recurrence and instead\\nrelying entirely on an attention mechanism to draw global dependencies between input and output.\\nThe Transformer allows for significantly more parallelization and can reach a new state of the art in\\ntranslation quality after being trained for as little as twelve hours on eight P100 GPUs.\\n2\\nBackground\\nThe goal of reducing sequential computation also forms the foundation of the Extended Neural GPU\\n[16], ByteNet [18] and ConvS2S [9], all of which use convolutional neural networks as basic building\\nblock, computing hidden representations in parallel for all input and output positions. In these models,\\nthe number of operations required to relate signals from two arbitrary input or output positions grows\\nin the distance between positions, linearly for ConvS2S and logarithmically for ByteNet. This makes\\nit more difficult to learn dependencies between distant positions [12]. In the Transformer this is\\nreduced to a constant number of operations, albeit at the cost of reduced effective resolution due\\nto averaging attention-weighted positions, an effect we counteract with Multi-Head Attention as\\ndescribed in section 3.2.\\nSelf-attention, sometimes called intra-attention is an attention mechanism relating different positions\\nof a single sequence in order to compute a representation of the sequence. Self-attention has been\\nused successfully in a variety of tasks including reading comprehension, abstractive summarization,\\ntextual entailment and learning task-independent sentence representations [4, 27, 28, 22].\\nEnd-to-end memory networks are based on a recurrent attention mechanism instead of sequence-\\naligned recurrence and have been shown to perform well on simple-language question answering and\\nlanguage modeling tasks [34].\\nTo the best of our knowledge, however, the Transformer is the first transduction model relying\\nentirely on self-attention to compute representations of its input and output without using sequence-\\naligned RNNs or convolution. In the following sections, we will describe the Transformer, motivate\\nself-attention and discuss its advantages over models such as [17, 18] and [9].\\n3\\nModel Architecture\\nMost competitive neural sequence transduction models have an encoder-decoder structure [5, 2, 35].\\nHere, the encoder maps an input sequence of symbol representations (x1, ..., xn) to a sequence\\nof continuous representations z = (z1, ..., zn). Given z, the decoder then generates an output\\nsequence (y1, ..., ym) of symbols one element at a time. At each step the model is auto-regressive\\n[10], consuming the previously generated symbols as additional input when generating the next.\\n2\\nFigure 1: The Transformer - model architecture.\\nThe Transformer follows this overall architecture using stacked self-attention and point-wise, fully\\nconnected layers for both the encoder and decoder, shown in the left and right halves of Figure 1,\\nrespectively.\\n3.1\\nEncoder and Decoder Stacks\\nEncoder:\\nThe encoder is composed of a stack of N = 6 identical layers. Each layer has two\\nsub-layers. The first is a multi-head self-attention mechanism, and the second is a simple, position-\\nwise fully connected feed-forward network. We employ a residual connection [11] around each of\\nthe two sub-layers, followed by layer normalization [1]. That is, the output of each sub-layer is\\nLayerNorm(x + Sublayer(x)), where Sublayer(x) is the function implemented by the sub-layer\\nitself. To facilitate these residual connections, all sub-layers in the model, as well as the embedding\\nlayers, produce outputs of dimension dmodel = 512.\\nDecoder:\\nThe decoder is also composed of a stack of N = 6 identical layers. In addition to the two\\nsub-layers in each encoder layer, the decoder inserts a third sub-layer, which performs multi-head\\nattention over the output of the encoder stack. Similar to the encoder, we employ residual connections\\naround each of the sub-layers, followed by layer normalization. We also modify the self-attention\\nsub-layer in the decoder stack to prevent positions from attending to subsequent positions. This\\nmasking, combined with fact that the output embeddings are offset by one position, ensures that the\\npredictions for position i can depend only on the known outputs at positions less than i.\\n3.2\\nAttention\\nAn attention function can be described as mapping a query and a set of key-value pairs to an output,\\nwhere the query, keys, values, and output are all vectors. The output is computed as a weighted sum\\n3\\nScaled Dot-Product Attention\\nMulti-Head Attention\\nFigure 2: (left) Scaled Dot-Product Attention. (right) Multi-Head Attention consists of several\\nattention layers running in parallel.\\nof the values, where the weight assigned to each value is computed by a compatibility function of the\\nquery with the corresponding key.\\n3.2.1\\nScaled Dot-Product Attention\\nWe call our particular attention \"Scaled Dot-Product Attention\" (Figure 2). The input consists of\\nqueries and keys of dimension dk, and values of dimension dv. We compute the dot products of the\\nquery with all keys, divide each by √dk, and apply a softmax function to obtain the weights on the\\nvalues.\\nIn practice, we compute the attention function on a set of queries simultaneously, packed together\\ninto a matrix Q. The keys and values are also packed together into matrices K and V . We compute\\nthe matrix of outputs as:\\nAttention(Q, K, V ) = softmax(QKT\\n√dk\\n)V\\n(1)\\nThe two most commonly used attention functions are additive attention [2], and dot-product (multi-\\nplicative) attention. Dot-product attention is identical to our algorithm, except for the scaling factor\\nof\\n1\\n√dk . Additive attention computes the compatibility function using a feed-forward network with\\na single hidden layer. While the two are similar in theoretical complexity, dot-product attention is\\nmuch faster and more space-efficient in practice, since it can be implemented using highly optimized\\nmatrix multiplication code.\\nWhile for small values of dk the two mechanisms perform similarly, additive attention outperforms\\ndot product attention without scaling for larger values of dk [3]. We suspect that for large values of\\ndk, the dot products grow large in magnitude, pushing the softmax function into regions where it has\\nextremely small gradients 4. To counteract this effect, we scale the dot products by\\n1\\n√dk .\\n3.2.2\\nMulti-Head Attention\\nInstead of performing a single attention function with dmodel-dimensional keys, values and queries,\\nwe found it beneficial to linearly project the queries, keys and values h times with different, learned\\nlinear projections to dk, dk and dv dimensions, respectively. On each of these projected versions of\\nqueries, keys and values we then perform the attention function in parallel, yielding dv-dimensional\\n4To illustrate why the dot products get large, assume that the components of q and k are independent random\\nvariables with mean 0 and variance 1. Then their dot product, q · k = Pdk\\ni=1 qiki, has mean 0 and variance dk.\\n4\\noutput values. These are concatenated and once again projected, resulting in the final values, as\\ndepicted in Figure 2.\\nMulti-head attention allows the model to jointly attend to information from different representation\\nsubspaces at different positions. With a single attention head, averaging inhibits this.\\nMultiHead(Q, K, V ) = Concat(head1, ..., headh)W O\\nwhere headi = Attention(QW Q\\ni , KW K\\ni , V W V\\ni )\\nWhere the projections are parameter matrices W Q\\ni\\n∈Rdmodel×dk, W K\\ni\\n∈Rdmodel×dk, W V\\ni\\n∈Rdmodel×dv\\nand W O ∈Rhdv×dmodel.\\nIn this work we employ h = 8 parallel attention layers, or heads. For each of these we use\\ndk = dv = dmodel/h = 64. Due to the reduced dimension of each head, the total computational cost\\nis similar to that of single-head attention with full dimensionality.\\n3.2.3\\nApplications of Attention in our Model\\nThe Transformer uses multi-head attention in three different ways:\\n• In \"encoder-decoder attention\" layers, the queries come from the previous decoder layer,\\nand the memory keys and values come from the output of the encoder. This allows every\\nposition in the decoder to attend over all positions in the input sequence. This mimics the\\ntypical encoder-decoder attention mechanisms in sequence-to-sequence models such as\\n[38, 2, 9].\\n• The encoder contains self-attention layers. In a self-attention layer all of the keys, values\\nand queries come from the same place, in this case, the output of the previous layer in the\\nencoder. Each position in the encoder can attend to all positions in the previous layer of the\\nencoder.\\n• Similarly, self-attention layers in the decoder allow each position in the decoder to attend to\\nall positions in the decoder up to and including that position. We need to prevent leftward\\ninformation flow in the decoder to preserve the auto-regressive property. We implement this\\ninside of scaled dot-product attention by masking out (setting to −∞) all values in the input\\nof the softmax which correspond to illegal connections. See Figure 2.\\n3.3\\nPosition-wise Feed-Forward Networks\\nIn addition to attention sub-layers, each of the layers in our encoder and decoder contains a fully\\nconnected feed-forward network, which is applied to each position separately and identically. This\\nconsists of two linear transformations with a ReLU activation in between.\\nFFN(x) = max(0, xW1 + b1)W2 + b2\\n(2)\\nWhile the linear transformations are the same across different positions, they use different parameters\\nfrom layer to layer. Another way of describing this is as two convolutions with kernel size 1.\\nThe dimensionality of input and output is dmodel = 512, and the inner-layer has dimensionality\\ndff = 2048.\\n3.4\\nEmbeddings and Softmax\\nSimilarly to other sequence transduction models, we use learned embeddings to convert the input\\ntokens and output tokens to vectors of dimension dmodel. We also use the usual learned linear transfor-\\nmation and softmax function to convert the decoder output to predicted next-token probabilities. In\\nour model, we share the same weight matrix between the two embedding layers and the pre-softmax\\nlinear transformation, similar to [30]. In the embedding layers, we multiply those weights by √dmodel.\\n5\\nTable 1: Maximum path lengths, per-layer complexity and minimum number of sequential operations\\nfor different layer types. n is the sequence length, d is the representation dimension, k is the kernel\\nsize of convolutions and r the size of the neighborhood in restricted self-attention.\\nLayer Type\\nComplexity per Layer\\nSequential\\nMaximum Path Length\\nOperations\\nSelf-Attention\\nO(n2 · d)\\nO(1)\\nO(1)\\nRecurrent\\nO(n · d2)\\nO(n)\\nO(n)\\nConvolutional\\nO(k · n · d2)\\nO(1)\\nO(logk(n))\\nSelf-Attention (restricted)\\nO(r · n · d)\\nO(1)\\nO(n/r)\\n3.5\\nPositional Encoding\\nSince our model contains no recurrence and no convolution, in order for the model to make use of the\\norder of the sequence, we must inject some information about the relative or absolute position of the\\ntokens in the sequence. To this end, we add \"positional encodings\" to the input embeddings at the\\nbottoms of the encoder and decoder stacks. The positional encodings have the same dimension dmodel\\nas the embeddings, so that the two can be summed. There are many choices of positional encodings,\\nlearned and fixed [9].\\nIn this work, we use sine and cosine functions of different frequencies:\\nPE(pos,2i) = sin(pos/100002i/dmodel)\\nPE(pos,2i+1) = cos(pos/100002i/dmodel)\\nwhere pos is the position and i is the dimension. That is, each dimension of the positional encoding\\ncorresponds to a sinusoid. The wavelengths form a geometric progression from 2π to 10000 · 2π. We\\nchose this function because we hypothesized it would allow the model to easily learn to attend by\\nrelative positions, since for any fixed offset k, PEpos+k can be represented as a linear function of\\nPEpos.\\nWe also experimented with using learned positional embeddings [9] instead, and found that the two\\nversions produced nearly identical results (see Table 3 row (E)). We chose the sinusoidal version\\nbecause it may allow the model to extrapolate to sequence lengths longer than the ones encountered\\nduring training.\\n4\\nWhy Self-Attention\\nIn this section we compare various aspects of self-attention layers to the recurrent and convolu-\\ntional layers commonly used for mapping one variable-length sequence of symbol representations\\n(x1, ..., xn) to another sequence of equal length (z1, ..., zn), with xi, zi ∈Rd, such as a hidden\\nlayer in a typical sequence transduction encoder or decoder. Motivating our use of self-attention we\\nconsider three desiderata.\\nOne is the total computational complexity per layer. Another is the amount of computation that can\\nbe parallelized, as measured by the minimum number of sequential operations required.\\nThe third is the path length between long-range dependencies in the network. Learning long-range\\ndependencies is a key challenge in many sequence transduction tasks. One key factor affecting the\\nability to learn such dependencies is the length of the paths forward and backward signals have to\\ntraverse in the network. The shorter these paths between any combination of positions in the input\\nand output sequences, the easier it is to learn long-range dependencies [12]. Hence we also compare\\nthe maximum path length between any two input and output positions in networks composed of the\\ndifferent layer types.\\nAs noted in Table 1, a self-attention layer connects all positions with a constant number of sequentially\\nexecuted operations, whereas a recurrent layer requires O(n) sequential operations. In terms of\\ncomputational complexity, self-attention layers are faster than recurrent layers when the sequence\\n6\\nlength n is smaller than the representation dimensionality d, which is most often the case with\\nsentence representations used by state-of-the-art models in machine translations, such as word-piece\\n[38] and byte-pair [31] representations. To improve computational performance for tasks involving\\nvery long sequences, self-attention could be restricted to considering only a neighborhood of size r in\\nthe input sequence centered around the respective output position. This would increase the maximum\\npath length to O(n/r). We plan to investigate this approach further in future work.\\nA single convolutional layer with kernel width k < n does not connect all pairs of input and output\\npositions. Doing so requires a stack of O(n/k) convolutional layers in the case of contiguous kernels,\\nor O(logk(n)) in the case of dilated convolutions [18], increasing the length of the longest paths\\nbetween any two positions in the network. Convolutional layers are generally more expensive than\\nrecurrent layers, by a factor of k. Separable convolutions [6], however, decrease the complexity\\nconsiderably, to O(k · n · d + n · d2). Even with k = n, however, the complexity of a separable\\nconvolution is equal to the combination of a self-attention layer and a point-wise feed-forward layer,\\nthe approach we take in our model.\\nAs side benefit, self-attention could yield more interpretable models. We inspect attention distributions\\nfrom our models and present and discuss examples in the appendix. Not only do individual attention\\nheads clearly learn to perform different tasks, many appear to exhibit behavior related to the syntactic\\nand semantic structure of the sentences.\\n5\\nTraining\\nThis section describes the training regime for our models.\\n5.1\\nTraining Data and Batching\\nWe trained on the standard WMT 2014 English-German dataset consisting of about 4.5 million\\nsentence pairs. Sentences were encoded using byte-pair encoding [3], which has a shared source-\\ntarget vocabulary of about 37000 tokens. For English-French, we used the significantly larger WMT\\n2014 English-French dataset consisting of 36M sentences and split tokens into a 32000 word-piece\\nvocabulary [38]. Sentence pairs were batched together by approximate sequence length. Each training\\nbatch contained a set of sentence pairs containing approximately 25000 source tokens and 25000\\ntarget tokens.\\n5.2\\nHardware and Schedule\\nWe trained our models on one machine with 8 NVIDIA P100 GPUs. For our base models using\\nthe hyperparameters described throughout the paper, each training step took about 0.4 seconds. We\\ntrained the base models for a total of 100,000 steps or 12 hours. For our big models,(described on the\\nbottom line of table 3), step time was 1.0 seconds. The big models were trained for 300,000 steps\\n(3.5 days).\\n5.3\\nOptimizer\\nWe used the Adam optimizer [20] with β1 = 0.9, β2 = 0.98 and ϵ = 10−9. We varied the learning\\nrate over the course of training, according to the formula:\\nlrate = d−0.5\\nmodel · min(step_num−0.5, step_num · warmup_steps−1.5)\\n(3)\\nThis corresponds to increasing the learning rate linearly for the first warmup_steps training steps,\\nand decreasing it thereafter proportionally to the inverse square root of the step number. We used\\nwarmup_steps = 4000.\\n5.4\\nRegularization\\nWe employ three types of regularization during training:\\n7\\nTable 2: The Transformer achieves better BLEU scores than previous state-of-the-art models on the\\nEnglish-to-German and English-to-French newstest2014 tests at a fraction of the training cost.\\nModel\\nBLEU\\nTraining Cost (FLOPs)\\nEN-DE\\nEN-FR\\nEN-DE\\nEN-FR\\nByteNet [18]\\n23.75\\nDeep-Att + PosUnk [39]\\n39.2\\n1.0 · 1020\\nGNMT + RL [38]\\n24.6\\n39.92\\n2.3 · 1019\\n1.4 · 1020\\nConvS2S [9]\\n25.16\\n40.46\\n9.6 · 1018\\n1.5 · 1020\\nMoE [32]\\n26.03\\n40.56\\n2.0 · 1019\\n1.2 · 1020\\nDeep-Att + PosUnk Ensemble [39]\\n40.4\\n8.0 · 1020\\nGNMT + RL Ensemble [38]\\n26.30\\n41.16\\n1.8 · 1020\\n1.1 · 1021\\nConvS2S Ensemble [9]\\n26.36\\n41.29\\n7.7 · 1019\\n1.2 · 1021\\nTransformer (base model)\\n27.3\\n38.1\\n3.3 · 1018\\nTransformer (big)\\n28.4\\n41.8\\n2.3 · 1019\\nResidual Dropout\\nWe apply dropout [33] to the output of each sub-layer, before it is added to the\\nsub-layer input and normalized. In addition, we apply dropout to the sums of the embeddings and the\\npositional encodings in both the encoder and decoder stacks. For the base model, we use a rate of\\nPdrop = 0.1.\\nLabel Smoothing\\nDuring training, we employed label smoothing of value ϵls = 0.1 [36]. This\\nhurts perplexity, as the model learns to be more unsure, but improves accuracy and BLEU score.\\n6\\nResults\\n6.1\\nMachine Translation\\nOn the WMT 2014 English-to-German translation task, the big transformer model (Transformer (big)\\nin Table 2) outperforms the best previously reported models (including ensembles) by more than 2.0\\nBLEU, establishing a new state-of-the-art BLEU score of 28.4. The configuration of this model is\\nlisted in the bottom line of Table 3. Training took 3.5 days on 8 P100 GPUs. Even our base model\\nsurpasses all previously published models and ensembles, at a fraction of the training cost of any of\\nthe competitive models.\\nOn the WMT 2014 English-to-French translation task, our big model achieves a BLEU score of 41.0,\\noutperforming all of the previously published single models, at less than 1/4 the training cost of the\\nprevious state-of-the-art model. The Transformer (big) model trained for English-to-French used\\ndropout rate Pdrop = 0.1, instead of 0.3.\\nFor the base models, we used a single model obtained by averaging the last 5 checkpoints, which\\nwere written at 10-minute intervals. For the big models, we averaged the last 20 checkpoints. We\\nused beam search with a beam size of 4 and length penalty α = 0.6 [38]. These hyperparameters\\nwere chosen after experimentation on the development set. We set the maximum output length during\\ninference to input length + 50, but terminate early when possible [38].\\nTable 2 summarizes our results and compares our translation quality and training costs to other model\\narchitectures from the literature. We estimate the number of floating point operations used to train a\\nmodel by multiplying the training time, the number of GPUs used, and an estimate of the sustained\\nsingle-precision floating-point capacity of each GPU 5.\\n6.2\\nModel Variations\\nTo evaluate the importance of different components of the Transformer, we varied our base model\\nin different ways, measuring the change in performance on English-to-German translation on the\\n5We used values of 2.8, 3.7, 6.0 and 9.5 TFLOPS for K80, K40, M40 and P100, respectively.\\n8\\nTable 3: Variations on the Transformer architecture. Unlisted values are identical to those of the base\\nmodel. All metrics are on the English-to-German translation development set, newstest2013. Listed\\nperplexities are per-wordpiece, according to our byte-pair encoding, and should not be compared to\\nper-word perplexities.\\nN\\ndmodel\\ndff\\nh\\ndk\\ndv\\nPdrop\\nϵls\\ntrain\\nPPL\\nBLEU\\nparams\\nsteps\\n(dev)\\n(dev)\\n×106\\nbase\\n6\\n512\\n2048\\n8\\n64\\n64\\n0.1\\n0.1\\n100K\\n4.92\\n25.8\\n65\\n(A)\\n1\\n512\\n512\\n5.29\\n24.9\\n4\\n128\\n128\\n5.00\\n25.5\\n16\\n32\\n32\\n4.91\\n25.8\\n32\\n16\\n16\\n5.01\\n25.4\\n(B)\\n16\\n5.16\\n25.1\\n58\\n32\\n5.01\\n25.4\\n60\\n(C)\\n2\\n6.11\\n23.7\\n36\\n4\\n5.19\\n25.3\\n50\\n8\\n4.88\\n25.5\\n80\\n256\\n32\\n32\\n5.75\\n24.5\\n28\\n1024\\n128\\n128\\n4.66\\n26.0\\n168\\n1024\\n5.12\\n25.4\\n53\\n4096\\n4.75\\n26.2\\n90\\n(D)\\n0.0\\n5.77\\n24.6\\n0.2\\n4.95\\n25.5\\n0.0\\n4.67\\n25.3\\n0.2\\n5.47\\n25.7\\n(E)\\npositional embedding instead of sinusoids\\n4.92\\n25.7\\nbig\\n6\\n1024\\n4096\\n16\\n0.3\\n300K\\n4.33\\n26.4\\n213\\ndevelopment set, newstest2013. We used beam search as described in the previous section, but no\\ncheckpoint averaging. We present these results in Table 3.\\nIn Table 3 rows (A), we vary the number of attention heads and the attention key and value dimensions,\\nkeeping the amount of computation constant, as described in Section 3.2.2. While single-head\\nattention is 0.9 BLEU worse than the best setting, quality also drops off with too many heads.\\nIn Table 3 rows (B), we observe that reducing the attention key size dk hurts model quality. This\\nsuggests that determining compatibility is not easy and that a more sophisticated compatibility\\nfunction than dot product may be beneficial. We further observe in rows (C) and (D) that, as expected,\\nbigger models are better, and dropout is very helpful in avoiding over-fitting. In row (E) we replace our\\nsinusoidal positional encoding with learned positional embeddings [9], and observe nearly identical\\nresults to the base model.\\n6.3\\nEnglish Constituency Parsing\\nTo evaluate if the Transformer can generalize to other tasks we performed experiments on English\\nconstituency parsing. This task presents specific challenges: the output is subject to strong structural\\nconstraints and is significantly longer than the input. Furthermore, RNN sequence-to-sequence\\nmodels have not been able to attain state-of-the-art results in small-data regimes [37].\\nWe trained a 4-layer transformer with dmodel = 1024 on the Wall Street Journal (WSJ) portion of the\\nPenn Treebank [25], about 40K training sentences. We also trained it in a semi-supervised setting,\\nusing the larger high-confidence and BerkleyParser corpora from with approximately 17M sentences\\n[37]. We used a vocabulary of 16K tokens for the WSJ only setting and a vocabulary of 32K tokens\\nfor the semi-supervised setting.\\nWe performed only a small number of experiments to select the dropout, both attention and residual\\n(section 5.4), learning rates and beam size on the Section 22 development set, all other parameters\\nremained unchanged from the English-to-German base translation model. During inference, we\\n9\\nTable 4: The Transformer generalizes well to English constituency parsing (Results are on Section 23\\nof WSJ)\\nParser\\nTraining\\nWSJ 23 F1\\nVinyals & Kaiser el al. (2014) [37]\\nWSJ only, discriminative\\n88.3\\nPetrov et al. (2006) [29]\\nWSJ only, discriminative\\n90.4\\nZhu et al. (2013) [40]\\nWSJ only, discriminative\\n90.4\\nDyer et al. (2016) [8]\\nWSJ only, discriminative\\n91.7\\nTransformer (4 layers)\\nWSJ only, discriminative\\n91.3\\nZhu et al. (2013) [40]\\nsemi-supervised\\n91.3\\nHuang & Harper (2009) [14]\\nsemi-supervised\\n91.3\\nMcClosky et al. (2006) [26]\\nsemi-supervised\\n92.1\\nVinyals & Kaiser el al. (2014) [37]\\nsemi-supervised\\n92.1\\nTransformer (4 layers)\\nsemi-supervised\\n92.7\\nLuong et al. (2015) [23]\\nmulti-task\\n93.0\\nDyer et al. (2016) [8]\\ngenerative\\n93.3\\nincreased the maximum output length to input length + 300. We used a beam size of 21 and α = 0.3\\nfor both WSJ only and the semi-supervised setting.\\nOur results in Table 4 show that despite the lack of task-specific tuning our model performs sur-\\nprisingly well, yielding better results than all previously reported models with the exception of the\\nRecurrent Neural Network Grammar [8].\\nIn contrast to RNN sequence-to-sequence models [37], the Transformer outperforms the Berkeley-\\nParser [29] even when training only on the WSJ training set of 40K sentences.\\n7\\nConclusion\\nIn this work, we presented the Transformer, the first sequence transduction model based entirely on\\nattention, replacing the recurrent layers most commonly used in encoder-decoder architectures with\\nmulti-headed self-attention.\\nFor translation tasks, the Transformer can be trained significantly faster than architectures based\\non recurrent or convolutional layers. On both WMT 2014 English-to-German and WMT 2014\\nEnglish-to-French translation tasks, we achieve a new state of the art. In the former task our best\\nmodel outperforms even all previously reported ensembles.\\nWe are excited about the future of attention-based models and plan to apply them to other tasks. We\\nplan to extend the Transformer to problems involving input and output modalities other than text and\\nto investigate local, restricted attention mechanisms to efficiently handle large inputs and outputs\\nsuch as images, audio and video. Making generation less sequential is another research goals of ours.\\nThe code we used to train and evaluate our models is available at https://github.com/\\ntensorflow/tensor2tensor.\\nAcknowledgements\\nWe are grateful to Nal Kalchbrenner and Stephan Gouws for their fruitful\\ncomments, corrections and inspiration.\\nReferences\\n[1] Jimmy Lei Ba, Jamie Ryan Kiros, and Geoffrey E Hinton. Layer normalization. arXiv preprint\\narXiv:1607.06450, 2016.\\n[2] Dzmitry Bahdanau, Kyunghyun Cho, and Yoshua Bengio. Neural machine translation by jointly\\nlearning to align and translate. CoRR, abs/1409.0473, 2014.\\n[3] Denny Britz, Anna Goldie, Minh-Thang Luong, and Quoc V. Le. Massive exploration of neural\\nmachine translation architectures. CoRR, abs/1703.03906, 2017.\\n[4] Jianpeng Cheng, Li Dong, and Mirella Lapata. Long short-term memory-networks for machine\\nreading. arXiv preprint arXiv:1601.06733, 2016.\\n10\\n[5] Kyunghyun Cho, Bart van Merrienboer, Caglar Gulcehre, Fethi Bougares, Holger Schwenk,\\nand Yoshua Bengio. Learning phrase representations using rnn encoder-decoder for statistical\\nmachine translation. CoRR, abs/1406.1078, 2014.\\n[6] Francois Chollet. Xception: Deep learning with depthwise separable convolutions. arXiv\\npreprint arXiv:1610.02357, 2016.\\n[7] Junyoung Chung, Çaglar Gülçehre, Kyunghyun Cho, and Yoshua Bengio. Empirical evaluation\\nof gated recurrent neural networks on sequence modeling. CoRR, abs/1412.3555, 2014.\\n[8] Chris Dyer, Adhiguna Kuncoro, Miguel Ballesteros, and Noah A. Smith. Recurrent neural\\nnetwork grammars. In Proc. of NAACL, 2016.\\n[9] Jonas Gehring, Michael Auli, David Grangier, Denis Yarats, and Yann N. Dauphin. Convolu-\\ntional sequence to sequence learning. arXiv preprint arXiv:1705.03122v2, 2017.\\n[10] Alex Graves.\\nGenerating sequences with recurrent neural networks.\\narXiv preprint\\narXiv:1308.0850, 2013.\\n[11] Kaiming He, Xiangyu Zhang, Shaoqing Ren, and Jian Sun. Deep residual learning for im-\\nage recognition. In Proceedings of the IEEE Conference on Computer Vision and Pattern\\nRecognition, pages 770–778, 2016.\\n[12] Sepp Hochreiter, Yoshua Bengio, Paolo Frasconi, and Jürgen Schmidhuber. Gradient flow in\\nrecurrent nets: the difficulty of learning long-term dependencies, 2001.\\n[13] Sepp Hochreiter and Jürgen Schmidhuber. Long short-term memory. Neural computation,\\n9(8):1735–1780, 1997.\\n[14] Zhongqiang Huang and Mary Harper. Self-training PCFG grammars with latent annotations\\nacross languages. In Proceedings of the 2009 Conference on Empirical Methods in Natural\\nLanguage Processing, pages 832–841. ACL, August 2009.\\n[15] Rafal Jozefowicz, Oriol Vinyals, Mike Schuster, Noam Shazeer, and Yonghui Wu. Exploring\\nthe limits of language modeling. arXiv preprint arXiv:1602.02410, 2016.\\n[16] Łukasz Kaiser and Samy Bengio. Can active memory replace attention? In Advances in Neural\\nInformation Processing Systems, (NIPS), 2016.\\n[17] Łukasz Kaiser and Ilya Sutskever. Neural GPUs learn algorithms. In International Conference\\non Learning Representations (ICLR), 2016.\\n[18] Nal Kalchbrenner, Lasse Espeholt, Karen Simonyan, Aaron van den Oord, Alex Graves, and Ko-\\nray Kavukcuoglu. Neural machine translation in linear time. arXiv preprint arXiv:1610.10099v2,\\n2017.\\n[19] Yoon Kim, Carl Denton, Luong Hoang, and Alexander M. Rush. Structured attention networks.\\nIn International Conference on Learning Representations, 2017.\\n[20] Diederik Kingma and Jimmy Ba. Adam: A method for stochastic optimization. In ICLR, 2015.\\n[21] Oleksii Kuchaiev and Boris Ginsburg. Factorization tricks for LSTM networks. arXiv preprint\\narXiv:1703.10722, 2017.\\n[22] Zhouhan Lin, Minwei Feng, Cicero Nogueira dos Santos, Mo Yu, Bing Xiang, Bowen\\nZhou, and Yoshua Bengio. A structured self-attentive sentence embedding. arXiv preprint\\narXiv:1703.03130, 2017.\\n[23] Minh-Thang Luong, Quoc V. Le, Ilya Sutskever, Oriol Vinyals, and Lukasz Kaiser. Multi-task\\nsequence to sequence learning. arXiv preprint arXiv:1511.06114, 2015.\\n[24] Minh-Thang Luong, Hieu Pham, and Christopher D Manning. Effective approaches to attention-\\nbased neural machine translation. arXiv preprint arXiv:1508.04025, 2015.\\n11\\n[25] Mitchell P Marcus, Mary Ann Marcinkiewicz, and Beatrice Santorini. Building a large annotated\\ncorpus of english: The penn treebank. Computational linguistics, 19(2):313–330, 1993.\\n[26] David McClosky, Eugene Charniak, and Mark Johnson. Effective self-training for parsing. In\\nProceedings of the Human Language Technology Conference of the NAACL, Main Conference,\\npages 152–159. ACL, June 2006.\\n[27] Ankur Parikh, Oscar Täckström, Dipanjan Das, and Jakob Uszkoreit. A decomposable attention\\nmodel. In Empirical Methods in Natural Language Processing, 2016.\\n[28] Romain Paulus, Caiming Xiong, and Richard Socher. A deep reinforced model for abstractive\\nsummarization. arXiv preprint arXiv:1705.04304, 2017.\\n[29] Slav Petrov, Leon Barrett, Romain Thibaux, and Dan Klein. Learning accurate, compact,\\nand interpretable tree annotation. In Proceedings of the 21st International Conference on\\nComputational Linguistics and 44th Annual Meeting of the ACL, pages 433–440. ACL, July\\n2006.\\n[30] Ofir Press and Lior Wolf. Using the output embedding to improve language models. arXiv\\npreprint arXiv:1608.05859, 2016.\\n[31] Rico Sennrich, Barry Haddow, and Alexandra Birch. Neural machine translation of rare words\\nwith subword units. arXiv preprint arXiv:1508.07909, 2015.\\n[32] Noam Shazeer, Azalia Mirhoseini, Krzysztof Maziarz, Andy Davis, Quoc Le, Geoffrey Hinton,\\nand Jeff Dean. Outrageously large neural networks: The sparsely-gated mixture-of-experts\\nlayer. arXiv preprint arXiv:1701.06538, 2017.\\n[33] Nitish Srivastava, Geoffrey E Hinton, Alex Krizhevsky, Ilya Sutskever, and Ruslan Salakhutdi-\\nnov. Dropout: a simple way to prevent neural networks from overfitting. Journal of Machine\\nLearning Research, 15(1):1929–1958, 2014.\\n[34] Sainbayar Sukhbaatar, Arthur Szlam, Jason Weston, and Rob Fergus. End-to-end memory\\nnetworks. In C. Cortes, N. D. Lawrence, D. D. Lee, M. Sugiyama, and R. Garnett, editors,\\nAdvances in Neural Information Processing Systems 28, pages 2440–2448. Curran Associates,\\nInc., 2015.\\n[35] Ilya Sutskever, Oriol Vinyals, and Quoc VV Le. Sequence to sequence learning with neural\\nnetworks. In Advances in Neural Information Processing Systems, pages 3104–3112, 2014.\\n[36] Christian Szegedy, Vincent Vanhoucke, Sergey Ioffe, Jonathon Shlens, and Zbigniew Wojna.\\nRethinking the inception architecture for computer vision. CoRR, abs/1512.00567, 2015.\\n[37] Vinyals & Kaiser, Koo, Petrov, Sutskever, and Hinton. Grammar as a foreign language. In\\nAdvances in Neural Information Processing Systems, 2015.\\n[38] Yonghui Wu, Mike Schuster, Zhifeng Chen, Quoc V Le, Mohammad Norouzi, Wolfgang\\nMacherey, Maxim Krikun, Yuan Cao, Qin Gao, Klaus Macherey, et al. Google’s neural machine\\ntranslation system: Bridging the gap between human and machine translation. arXiv preprint\\narXiv:1609.08144, 2016.\\n[39] Jie Zhou, Ying Cao, Xuguang Wang, Peng Li, and Wei Xu. Deep recurrent models with\\nfast-forward connections for neural machine translation. CoRR, abs/1606.04199, 2016.\\n[40] Muhua Zhu, Yue Zhang, Wenliang Chen, Min Zhang, and Jingbo Zhu. Fast and accurate\\nshift-reduce constituent parsing. In Proceedings of the 51st Annual Meeting of the ACL (Volume\\n1: Long Papers), pages 434–443. ACL, August 2013.\\n12\\nAttention Visualizations\\nIt\\nis\\nin\\nthis\\nspirit\\nthat\\na\\nmajority\\nof\\nAmerican\\ngovernments\\nhave\\npassed\\nnew\\nlaws\\nsince\\n2009\\nmaking\\nthe\\nregistration\\nor\\nvoting\\nprocess\\nmore\\ndifficult\\n.\\n<EOS>\\n<pad>\\n<pad>\\n<pad>\\n<pad>\\n<pad>\\n<pad>\\nIt\\nis\\nin\\nthis\\nspirit\\nthat\\na\\nmajority\\nof\\nAmerican\\ngovernments\\nhave\\npassed\\nnew\\nlaws\\nsince\\n2009\\nmaking\\nthe\\nregistration\\nor\\nvoting\\nprocess\\nmore\\ndifficult\\n.\\n<EOS>\\n<pad>\\n<pad>\\n<pad>\\n<pad>\\n<pad>\\n<pad>\\nFigure 3: An example of the attention mechanism following long-distance dependencies in the\\nencoder self-attention in layer 5 of 6. Many of the attention heads attend to a distant dependency of\\nthe verb ‘making’, completing the phrase ‘making...more difficult’. Attentions here shown only for\\nthe word ‘making’. Different colors represent different heads. Best viewed in color.\\n13\\nThe\\nLaw\\nwill\\nnever\\nbe\\nperfect\\n,\\nbut\\nits\\napplication\\nshould\\nbe\\njust\\n-\\nthis\\nis\\nwhat\\nwe\\nare\\nmissing\\n,\\nin\\nmy\\nopinion\\n.\\n<EOS>\\n<pad>\\nThe\\nLaw\\nwill\\nnever\\nbe\\nperfect\\n,\\nbut\\nits\\napplication\\nshould\\nbe\\njust\\n-\\nthis\\nis\\nwhat\\nwe\\nare\\nmissing\\n,\\nin\\nmy\\nopinion\\n.\\n<EOS>\\n<pad>\\nThe\\nLaw\\nwill\\nnever\\nbe\\nperfect\\n,\\nbut\\nits\\napplication\\nshould\\nbe\\njust\\n-\\nthis\\nis\\nwhat\\nwe\\nare\\nmissing\\n,\\nin\\nmy\\nopinion\\n.\\n<EOS>\\n<pad>\\nThe\\nLaw\\nwill\\nnever\\nbe\\nperfect\\n,\\nbut\\nits\\napplication\\nshould\\nbe\\njust\\n-\\nthis\\nis\\nwhat\\nwe\\nare\\nmissing\\n,\\nin\\nmy\\nopinion\\n.\\n<EOS>\\n<pad>\\nFigure 4: Two attention heads, also in layer 5 of 6, apparently involved in anaphora resolution. Top:\\nFull attentions for head 5. Bottom: Isolated attentions from just the word ‘its’ for attention heads 5\\nand 6. Note that the attentions are very sharp for this word.\\n14\\nThe\\nLaw\\nwill\\nnever\\nbe\\nperfect\\n,\\nbut\\nits\\napplication\\nshould\\nbe\\njust\\n-\\nthis\\nis\\nwhat\\nwe\\nare\\nmissing\\n,\\nin\\nmy\\nopinion\\n.\\n<EOS>\\n<pad>\\nThe\\nLaw\\nwill\\nnever\\nbe\\nperfect\\n,\\nbut\\nits\\napplication\\nshould\\nbe\\njust\\n-\\nthis\\nis\\nwhat\\nwe\\nare\\nmissing\\n,\\nin\\nmy\\nopinion\\n.\\n<EOS>\\n<pad>\\nThe\\nLaw\\nwill\\nnever\\nbe\\nperfect\\n,\\nbut\\nits\\napplication\\nshould\\nbe\\njust\\n-\\nthis\\nis\\nwhat\\nwe\\nare\\nmissing\\n,\\nin\\nmy\\nopinion\\n.\\n<EOS>\\n<pad>\\nThe\\nLaw\\nwill\\nnever\\nbe\\nperfect\\n,\\nbut\\nits\\napplication\\nshould\\nbe\\njust\\n-\\nthis\\nis\\nwhat\\nwe\\nare\\nmissing\\n,\\nin\\nmy\\nopinion\\n.\\n<EOS>\\n<pad>\\nFigure 5: Many of the attention heads exhibit behaviour that seems related to the structure of the\\nsentence. We give two such examples above, from two different heads from the encoder self-attention\\nat layer 5 of 6. The heads clearly learned to perform different tasks.\\n15\\n')]"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["#Arxiv\n", "from langchain_community.document_loaders import ArxivLoader\n", "docs = ArxivLoader(query=\"1706.03762\", load_max_docs=2).load()\n", "docs"]}, {"cell_type": "code", "execution_count": 22, "id": "d5edd048", "metadata": {}, "outputs": [{"data": {"text/plain": ["1"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["len(docs)"]}, {"cell_type": "code", "execution_count": 23, "id": "6dbdfe1d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[Document(metadata={'title': 'Generative artificial intelligence', 'summary': 'Generative artificial intelligence (Generative AI, GenAI, or GAI) is a subfield of artificial intelligence that uses generative models to produce text, images, videos, or other forms of data. These models learn the underlying patterns and structures of their training data and use them to produce new data based on the input, which often comes in the form of natural language prompts.\\nGenerative AI tools have become more common since an \"AI boom\" in the 2020s. This boom was made possible by improvements in transformer-based deep neural networks, particularly large language models (LLMs). Major tools include chatbots such as ChatGPT, Copilot, Gemini, Grok, and DeepSeek; text-to-image artificial intelligence image generation systems such as Stable Diffusion, Midjourney, and DALL-E; and text-to-video AI generators such as Sora. Technology companies developing generative AI include OpenAI, Anthropic, Meta AI, Microsoft, Google, DeepSeek, and Baidu.\\nGenerative AI has raised many ethical questions. It can be used for cybercrime, or to deceive or manipulate people through fake news or deepfakes. Even if used ethically, it may lead to mass replacement of human jobs. The tools themselves have been criticized as violating intellectual property laws, since they are trained on and emulate copyrighted works of art.\\nGenerative AI is used across many industries. Examples include software development, healthcare, finance, entertainment, customer service, sales and marketing, art, writing, fashion, and product design.', 'source': 'https://en.wikipedia.org/wiki/Generative_artificial_intelligence'}, page_content='Generative artificial intelligence (Generative AI, GenAI, or GAI) is a subfield of artificial intelligence that uses generative models to produce text, images, videos, or other forms of data. These models learn the underlying patterns and structures of their training data and use them to produce new data based on the input, which often comes in the form of natural language prompts.\\nGenerative AI tools have become more common since an \"AI boom\" in the 2020s. This boom was made possible by improvements in transformer-based deep neural networks, particularly large language models (LLMs). Major tools include chatbots such as ChatGPT, Copilot, Gemini, Grok, and DeepSeek; text-to-image artificial intelligence image generation systems such as Stable Diffusion, Midjourney, and DALL-E; and text-to-video AI generators such as Sora. Technology companies developing generative AI include OpenAI, Anthropic, Meta AI, Microsoft, Google, DeepSeek, and Baidu.\\nGenerative AI has raised many ethical questions. It can be used for cybercrime, or to deceive or manipulate people through fake news or deepfakes. Even if used ethically, it may lead to mass replacement of human jobs. The tools themselves have been criticized as violating intellectual property laws, since they are trained on and emulate copyrighted works of art.\\nGenerative AI is used across many industries. Examples include software development, healthcare, finance, entertainment, customer service, sales and marketing, art, writing, fashion, and product design.\\n\\n\\n== History ==\\n\\n\\n=== Early history ===\\nThe first example of an algorithmically generated media is likely the Markov chain. Markov chains have long been used to model natural languages since their development by Russian mathematician Andrey Markov in the early 20th century. Markov published his first paper on the topic in 1906, and analyzed the pattern of vowels and consonants in the novel Eugeny Onegin using Markov chains. Once a Markov chain is learned on a text corpus, it can then be used as a probabilistic text generator.\\nComputers were needed to go beyond Markov chains. By the early 1970s, Harold Cohen was creating and exhibiting generative AI works created by AARON, the computer program Cohen created to generate paintings.\\nThe terms generative AI planning or generative planning were used in the 1980s and 1990s to refer to AI planning systems, especially computer-aided process planning, used to generate sequences of actions to reach a specified goal. Generative AI planning systems used symbolic AI methods such as state space search and constraint satisfaction and were a \"relatively mature\" technology by the early 1990s. They were used to generate crisis action plans for military use, process plans for manufacturing and decision plans such as in prototype autonomous spacecraft.\\n\\n\\n=== Generative neural networks (2014-2019) ===\\n\\nSince inception, the field of machine learning has used both discriminative models and generative models to model and predict data. Beginning in the late 2000s decade, the emergence of deep learning drove progress, and research in image classification, speech recognition, natural language processing and other tasks. Neural networks in this era were typically trained as discriminative models due to the difficulty of generative modeling.\\nIn 2014, advancements such as the variational autoencoder and generative adversarial network produced the first practical deep neural networks capable of learning generative models, as opposed to discriminative ones, for complex data such as images. These deep generative models were the first to output not only class labels for images but also entire images.\\nIn 2017, the Transformer network enabled advancements in generative models compared to older Long-Short Term Memory models, leading to the first generative pre-trained transformer (GPT), known as GPT-1, in 2018. This was followed in 2019 by GPT-2, which demonstrated the ability to generalize unsupervised to many differen'), Document(metadata={'title': 'Generative AI pornography', 'summary': 'Generative AI pornography or simply AI pornography is a digitally created pornography produced through generative artificial intelligence (AI) technologies. Unlike traditional pornography, which involves real actors and cameras, this content is synthesized entirely by AI algorithms. These algorithms, including Generative adversarial network (GANs) and text-to-image models, generate lifelike images, videos, or animations from textual descriptions or datasets.\\n\\n', 'source': 'https://en.wikipedia.org/wiki/Generative_AI_pornography'}, page_content='Generative AI pornography or simply AI pornography is a digitally created pornography produced through generative artificial intelligence (AI) technologies. Unlike traditional pornography, which involves real actors and cameras, this content is synthesized entirely by AI algorithms. These algorithms, including Generative adversarial network (GANs) and text-to-image models, generate lifelike images, videos, or animations from textual descriptions or datasets.\\n\\n\\n== History ==\\nThe use of generative AI in the adult industry began in the late 2010s, initially focusing on AI-generated art, music, and visual content. This trend accelerated in 2022 with Stability AI\\'s release of Stable Diffusion (SD), an open-source text-to-image model that enables users to generate images, including NSFW content, from text prompts using the LAION-Aesthetics subset of the LAION-5B dataset. Despite Stability AI\\'s warnings against sexual imagery, SD\\'s public release led to dedicated communities exploring both artistic and explicit content, sparking ethical debates over open-access AI and its use in adult media. By 2020, AI tools had advanced to generate highly realistic adult content, amplifying calls for regulation.\\n\\n\\n=== AI-generated influencers ===\\nOne application of generative AI technology is the creation of AI-generated influencers on platforms such as OnlyFans and Instagram. These AI personas interact with users in ways that can mimic real human engagement, offering an entirely synthetic but convincing experience. While popular among niche audiences, these virtual influencers have prompted discussions about authenticity, consent, and the blurring line between human and AI-generated content, especially in adult entertainment.\\n\\n\\n=== The growth of AI porn sites ===\\nBy 2023, websites dedicated to AI-generated adult content had gained traction, catering to audiences seeking customizable experiences. These platforms allow users to create or view AI-generated pornography tailored to their preferences. These platforms enable users to create or view AI-generated adult content appealing to different preferences through prompts and tags, customizing body type, facial features, and art styles. Tags further refine the output, creating niche and diverse content. Many sites feature extensive image libraries and continuous content feeds, combining personalization with discovery and enhancing user engagement. AI porn sites, therefore, attract those seeking unique or niche experiences, sparking debates on creativity and the ethical boundaries of AI in adult media.\\n\\n\\n== Ethical concerns and misuse ==\\nThe growth of generative AI pornography has also attracted some cause for criticism. AI technology can be exploited to create non-consensual pornographic material, posing risks similar to those seen with deepfake revenge porn and AI-generated NCII (Non-Consensual Intimate Image). A 2023 analysis found that 98% of deepfake videos online are pornographic, with 99% of the victims being women. Some famous celebrities victims of deepfake include Scarlett Johansson, Taylor Swift, and Maisie Williams.\\nOpenAI is exploring whether NSFW content, such as erotica, can be responsibly generated in age-appropriate contexts while maintaining its ban on deepfakes. This proposal has attracted criticism from child safety campaigners who argue it undermines OpenAI\\'s mission to develop \"safe and beneficial\" AI. Additionally, the Internet Watch Foundation has raised concerns about AI being used to generate sexual abuse content involving children.\\n\\n\\n=== AI-generated non-consensual intimate imagery (AI Undress) ===\\nSeveral US states are taking actions against using deepfake apps and sharing them on the internet. In 2024, San Francisco filed a landmark lawsuit to shut down \"undress\" apps that allow users to generate non-consensual AI nude images, citing violations of state laws. The case aligns with California\\'s recent legislation—SB 926, SB 942, and SB 981—championed by Senators Aisha Wahab a'), Document(metadata={'title': 'AI boom', 'summary': 'The AI boom is an ongoing period of rapid progress in the field of artificial intelligence (AI) that started in the late 2010s before gaining international prominence in the early 2020s. Examples include large language models and generative AI applications developed by OpenAI as well as protein folding prediction led by Google DeepMind. This period is sometimes referred to as an AI spring, to contrast it with previous AI winters.\\n\\n', 'source': 'https://en.wikipedia.org/wiki/AI_boom'}, page_content='The AI boom is an ongoing period of rapid progress in the field of artificial intelligence (AI) that started in the late 2010s before gaining international prominence in the early 2020s. Examples include large language models and generative AI applications developed by OpenAI as well as protein folding prediction led by Google DeepMind. This period is sometimes referred to as an AI spring, to contrast it with previous AI winters.\\n\\n\\n== History ==\\n\\nIn 2012, a University of Toronto research team used artificial neural networks and deep learning techniques to lower the error rate below 25% for the first time during the ImageNet challenge for object recognition in computer vision. The event catalyzed the AI boom later that decade, when many alumni of the ImageNet challenge became leaders in the tech industry. In March 2016, AlphaGo beat Lee Sedol in a five-game match, marking the first time a computer Go program had beaten a 9-dan professional without handicap. This match led to significant increase in public interest in AI. The generative AI race began in earnest in 2016 or 2017 following the founding of OpenAI and earlier advances made in graphical processing units (GPUs), the amount and quality of training data, generative adversarial networks, diffusion models and transformer architectures. \\nIn 2018, the Artificial Intelligence Index, an initiative from Stanford University, reported a global explosion of commercial and research efforts in AI. Europe published the largest number of papers in the field that year, followed by China and North America. Technologies such as AlphaFold led to more accurate predictions of protein folding and improved the process of drug development. Economists and lawmakers began to discuss the potential impact of AI more frequently. By 2022, large language models (LLMs) saw increased usage in chatbot applications; text-to-image-models could generate images that appeared to be human-made; and speech synthesis software was able to replicate human speech efficiently.\\nAccording to metrics from 2017 to 2021, the United States outranks the rest of the world in terms of venture capital funding, the number of startups, and patents granted in AI. Scientists who have immigrated to the U.S. play an outsized role in the country\\'s development of AI technology. Many of them were educated in China, prompting debates about national security concerns amid worsening relations between the two countries.\\nExperts have framed AI development as a competition for economic and geopolitical advantage between the United States and China. In 2021, an analyst for the Council on Foreign Relations outlined ways that the U.S. could maintain its position amid progress made by China. In 2023, an analyst at the Center for Strategic and International Studies advocated for the U.S. to use its dominance in AI technology to drive its foreign policy instead of relying on trade agreements.\\n\\n\\n== Advances ==\\n\\n\\n=== Biomedical ===\\nThe AlphaFold 2 score of more than 90 in CASP\\'s global distance test (GDT) is considered a significant achievement in computational biology and great progress towards a decades-old grand challenge of biology. The structural biologist and Nobel Prize winner Venki Ramakrishnan called the result \"a stunning advance on the protein folding problem\", adding that \"It has occurred decades before many people in the field would have predicted.\"\\nThe ability to predict protein structures accurately based on the constituent amino acid sequence is expected to accelerate drug discovery and enable a better understanding of diseases.\\n\\n\\n=== Images and videos ===\\n\\nText-to-image models captured widespread public attention when OpenAI announced DALL-E, a transformer system, in January 2021. A successor capable of generating complex and realistic images, DALL-E 2, was unveiled in April 2022. An alternative text-to-image model, Midjourney, was released in July 2022. Another alternative, open-source model Stable Diffusion, released in August 2'), Document(metadata={'title': 'Artificial intelligence', 'summary': 'Artificial intelligence (AI) is the capability of computational systems to perform tasks typically associated with human intelligence, such as learning, reasoning, problem-solving, perception, and decision-making. It is a field of research in computer science that develops and studies methods and software that enable machines to perceive their environment and use learning and intelligence to take actions that maximize their chances of achieving defined goals.\\nHigh-profile applications of AI include advanced web search engines (e.g., Google Search); recommendation systems (used by YouTube, Amazon, and Netflix); virtual assistants (e.g., Google Assistant, Siri, and Alexa); autonomous vehicles (e.g., Waymo); generative and creative tools (e.g., ChatGPT and AI art); and superhuman play and analysis in strategy games (e.g., chess and Go). However, many AI applications are not perceived as AI: \"A lot of cutting edge AI has filtered into general applications, often without being called AI because once something becomes useful enough and common enough it\\'s not labeled AI anymore.\"\\nVarious subfields of AI research are centered around particular goals and the use of particular tools. The traditional goals of AI research include learning, reasoning, knowledge representation, planning, natural language processing, perception, and support for robotics. To reach these goals, AI researchers have adapted and integrated a wide range of techniques, including search and mathematical optimization, formal logic, artificial neural networks, and methods based on statistics, operations research, and economics. AI also draws upon psychology, linguistics, philosophy, neuroscience, and other fields. Some companies, such as OpenAI, Google DeepMind and Meta, aim to create artificial general intelligence (AGI)—AI that can complete virtually any cognitive task at least as well as a human.\\nArtificial intelligence was founded as an academic discipline in 1956, and the field went through multiple cycles of optimism throughout its history, followed by periods of disappointment and loss of funding, known as AI winters. Funding and interest vastly increased after 2012 when graphics processing units started being used to accelerate neural networks, and deep learning outperformed previous AI techniques. This growth accelerated further after 2017 with the transformer architecture. In the 2020s, the period of rapid progress marked by advanced generative AI became known as the AI boom. Generative AI and its ability to create and modify content exposed several unintended consequences and harms in the present and raised ethical concerns about AI\\'s long-term effects and potential existential risks, prompting discussions about regulatory policies to ensure the safety and benefits of the technology.\\n\\n', 'source': 'https://en.wikipedia.org/wiki/Artificial_intelligence'}, page_content='Artificial intelligence (AI) is the capability of computational systems to perform tasks typically associated with human intelligence, such as learning, reasoning, problem-solving, perception, and decision-making. It is a field of research in computer science that develops and studies methods and software that enable machines to perceive their environment and use learning and intelligence to take actions that maximize their chances of achieving defined goals.\\nHigh-profile applications of AI include advanced web search engines (e.g., Google Search); recommendation systems (used by YouTube, Amazon, and Netflix); virtual assistants (e.g., Google Assistant, Siri, and Alexa); autonomous vehicles (e.g., Waymo); generative and creative tools (e.g., ChatGPT and AI art); and superhuman play and analysis in strategy games (e.g., chess and Go). However, many AI applications are not perceived as AI: \"A lot of cutting edge AI has filtered into general applications, often without being called AI because once something becomes useful enough and common enough it\\'s not labeled AI anymore.\"\\nVarious subfields of AI research are centered around particular goals and the use of particular tools. The traditional goals of AI research include learning, reasoning, knowledge representation, planning, natural language processing, perception, and support for robotics. To reach these goals, AI researchers have adapted and integrated a wide range of techniques, including search and mathematical optimization, formal logic, artificial neural networks, and methods based on statistics, operations research, and economics. AI also draws upon psychology, linguistics, philosophy, neuroscience, and other fields. Some companies, such as OpenAI, Google DeepMind and Meta, aim to create artificial general intelligence (AGI)—AI that can complete virtually any cognitive task at least as well as a human.\\nArtificial intelligence was founded as an academic discipline in 1956, and the field went through multiple cycles of optimism throughout its history, followed by periods of disappointment and loss of funding, known as AI winters. Funding and interest vastly increased after 2012 when graphics processing units started being used to accelerate neural networks, and deep learning outperformed previous AI techniques. This growth accelerated further after 2017 with the transformer architecture. In the 2020s, the period of rapid progress marked by advanced generative AI became known as the AI boom. Generative AI and its ability to create and modify content exposed several unintended consequences and harms in the present and raised ethical concerns about AI\\'s long-term effects and potential existential risks, prompting discussions about regulatory policies to ensure the safety and benefits of the technology.\\n\\n\\n== Goals ==\\nThe general problem of simulating (or creating) intelligence has been broken into subproblems. These consist of particular traits or capabilities that researchers expect an intelligent system to display. The traits described below have received the most attention and cover the scope of AI research.\\n\\n\\n=== Reasoning and problem-solving ===\\nEarly researchers developed algorithms that imitated step-by-step reasoning that humans use when they solve puzzles or make logical deductions. By the late 1980s and 1990s, methods were developed for dealing with uncertain or incomplete information, employing concepts from probability and economics.\\nMany of these algorithms are insufficient for solving large reasoning problems because they experience a \"combinatorial explosion\": They become exponentially slower as the problems grow. Even humans rarely use the step-by-step deduction that early AI research could model. They solve most of their problems using fast, intuitive judgments. Accurate and efficient reasoning is an unsolved problem.\\n\\n\\n=== Knowledge representation ===\\n\\nKnowledge representation and knowledge engineering allow AI programs to answer questions intelligently and m')]\n"]}], "source": ["from langchain_community.document_loaders import WikipediaLoader\n", "docs = WikipediaLoader(query=\"Generative AI\", load_max_docs=4).load()\n", "len(docs)\n", "print(docs)"]}, {"cell_type": "code", "execution_count": null, "id": "ee003cdc", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "229681be", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "a3b7843a", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "0e46025d", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "3c70c53c", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "8ca1adcd", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "fd3c992e", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "ae51e7d1", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "80ad45d5", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "352a2124", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "agentic-2", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.18"}}, "nbformat": 4, "nbformat_minor": 5}